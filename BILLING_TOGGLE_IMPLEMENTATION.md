# Billing Entry Toggle Control Implementation

This document describes the comprehensive billing entry toggle control system that ensures proper billing cycle management with validation requirements.

## Overview

The billing toggle control system allows admin users to enable/disable billing entry creation for buildings while enforcing strict validation rules to ensure billing cycles are properly completed before being closed.

## Key Features

### 1. Toggle State Management
- **Field**: `billingEnabled` boolean in `building_pricing` table
- **Control**: Admin-only toggle switch in AdminSettings
- **Timestamp**: Uses `updated_at` field to track when billing was enabled

### 2. Toggle ON Behavior
- ✅ Records timestamp when billing is enabled (`updated_at` in `building_pricing`)
- ✅ Allows creation of new billing entries for all rooms in the building
- ✅ Clears any previous validation status
- ✅ No restrictions for enabling billing

### 3. Toggle OFF Validation
The toggle can only be turned OFF when ALL conditions are met:
- ✅ Every room in the building has at least one billing entry
- ✅ All billing entries were created AFTER the billing-enabled timestamp
- ✅ No rooms have `hasBillingEntry: false`

### 4. Validation Logic
- ✅ Queries all rooms in the building
- ✅ Checks billing entry existence for each room
- ✅ Verifies billing entry timestamps against billing-enabled date
- ✅ Provides detailed error messages with specific room numbers

### 5. UI Feedback
- ✅ Clear messaging about validation failures
- ✅ Lists specific rooms that need billing entries
- ✅ Shows billing-enabled date for reference
- ✅ Expandable details section for validation status

## Implementation Details

### Backend Changes

#### 1. Enhanced BillingService (`backend/src/billing/billing.service.ts`)

**New Methods:**
```typescript
// Helper method to analyze room billing status (refactored from duplication)
private async analyzeRoomBillingStatus(buildingId: string, billingEnabledDate: Date): Promise<{roomsWithoutBilling: string[]; roomsWithOldBilling: string[];}>

// Validates if billing can be turned OFF
private async validateBillingToggleOff(buildingId: string, billingEnabledDate: Date): Promise<void>

// Gets validation status for UI display
async getBillingToggleValidation(buildingId: string)
```

**Updated Methods:**
```typescript
// Enhanced to include validation before turning OFF
async updateBuildingPricing(buildingId: string, updateBuildingPricingDto: UpdateBuildingPricingDto, userId: string, userRole: string)
```

**Validation Logic:**
- Queries rooms with billing entries created after billing-enabled date
- Identifies rooms without billing entries
- Identifies rooms with only old billing entries
- Throws `BadRequestException` with detailed error messages

#### 2. Enhanced BillingController (`backend/src/billing/billing.controller.ts`)

**New Endpoint:**
```typescript
@Get('buildings/:buildingId/toggle-validation')
getBillingToggleValidation(@Param('buildingId') buildingId: string)
```

### Frontend Changes

#### 1. Enhanced AdminSettings (`frontend/src/components/admin/AdminSettings.tsx`)

**New State:**
```typescript
const [validationStatus, setValidationStatus] = useState<{
  canToggleOff: boolean;
  billingEnabled: boolean;
  billingEnabledDate: string | null;
  roomsWithoutBilling: string[];
  roomsWithOldBilling: string[];
  message: string;
} | null>(null);
const [showValidationDetails, setShowValidationDetails] = useState(false);
```

**Enhanced Functions:**
```typescript
// Fetches validation status when billing is enabled
const fetchValidationStatus = async () => { ... }

// Enhanced toggle with validation checks
const toggleBilling = async () => { ... }
```

**Enhanced UI:**
- Billing cycle status indicator
- Expandable validation details
- Room-specific error messages
- Billing-enabled date display

#### 2. Enhanced Services

**BillingService (`frontend/src/lib/services/billing.ts`):**
```typescript
// New method to get validation status
async getBillingToggleValidation(buildingId: string)
```

**API Layer (`frontend/src/lib/api.ts`):**
```typescript
// New API endpoint
getBillingToggleValidation: async (buildingId: string)
```

## Usage Flow

### Enabling Billing (Toggle ON)
1. Admin clicks toggle switch
2. `billingEnabled` set to `true`
3. `updated_at` timestamp recorded
4. Users can now create billing entries
5. Validation status cleared

### Disabling Billing (Toggle OFF)
1. Admin clicks toggle switch
2. System fetches validation status
3. **If validation fails:**
   - Toggle remains ON
   - Error message displayed
   - Validation details shown
   - Specific rooms listed
4. **If validation passes:**
   - `billingEnabled` set to `false`
   - Billing cycle closed
   - No new billing entries allowed

## Validation Rules

### Room Requirements
Each room must have:
- ✅ At least one billing entry
- ✅ Most recent billing entry created after billing-enabled date
- ✅ `hasBillingEntry: true` status

### Error Scenarios
1. **Rooms without billing entries:**
   - Error: "The following rooms have no billing entries: [room numbers]"
   
2. **Rooms with outdated billing entries:**
   - Error: "The following rooms have billing entries that were created before billing was enabled ([date]): [room numbers]"

3. **Combined errors:**
   - Both error types displayed with specific room lists

## UI Components

### Toggle Switch
- Visual indicator (ON/OFF)
- Color-coded (green/gray)
- Click handler with validation

### Validation Status Panel
- **Billing Cycle Status**: Shows completion status
- **Status Indicator**: ✅ Ready / ⚠️ Incomplete
- **Show/Hide Details**: Expandable section
- **Billing Enabled Date**: Reference timestamp
- **Room Lists**: Specific rooms needing attention

### Error Display
- Clear error messages
- Room-specific feedback
- Actionable information

## Database Schema

### BuildingPricing Table
```sql
-- Existing fields used for toggle control
billingEnabled: boolean (default: false)
updatedAt: timestamp (tracks when billing was enabled)
```

### Validation Queries
```sql
-- Check rooms and their billing entries
SELECT r.id, r.roomNumber, be.createdAt
FROM rooms r
LEFT JOIN billing_entries be ON r.id = be.room_id
WHERE r.building_id = ? AND be.created_at >= ?
```

## Benefits

### 1. Data Integrity
- Ensures complete billing cycles
- Prevents premature billing closure
- Maintains audit trail with timestamps

### 2. User Experience
- Clear validation feedback
- Specific actionable information
- Prevents user confusion

### 3. Business Logic
- Enforces proper billing workflows
- Ensures all rooms are billed before cycle closure
- Maintains billing period consistency

### 4. Administrative Control
- Admin-only access to toggle
- Comprehensive validation before changes
- Detailed status reporting

## Error Handling

### Backend Validation
- `BadRequestException` for validation failures
- Detailed error messages with room numbers
- Date formatting for user clarity

### Frontend Error Display
- Error state management
- User-friendly error messages
- Validation details expansion

### Recovery Actions
- Clear guidance on required actions
- Room-specific requirements
- Date reference for billing entries

## Testing Scenarios

### 1. Enable Billing
- ✅ Toggle turns ON successfully
- ✅ Timestamp recorded
- ✅ Users can create billing entries

### 2. Disable Billing - Success
- ✅ All rooms have recent billing entries
- ✅ Toggle turns OFF successfully
- ✅ Validation status shows "Ready"

### 3. Disable Billing - Failure
- ✅ Some rooms missing billing entries
- ✅ Toggle remains ON
- ✅ Error message displayed
- ✅ Specific rooms listed

### 4. Validation Status Display
- ✅ Status indicator shows correct state
- ✅ Details expand/collapse correctly
- ✅ Room lists display accurately
- ✅ Dates format correctly

This implementation ensures robust billing cycle management with comprehensive validation and user-friendly feedback systems.
