# Database Configuration
# For local development
DATABASE_URL="postgresql://postgres:postgres@localhost:5433/scapartmentdb"

# For Docker Compose
# DATABASE_URL="********************************************/scapartmentdb"

# For production (replace with your actual database credentials)
# DATABASE_URL="postgresql://username:password@host:port/database"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="7d"

# Application Configuration
PORT=3001
NODE_ENV=development

# Optional: Prisma Configuration
# PRISMA_CLI_QUERY_ENGINE_TYPE=binary
# PRISMA_CLI_BINARY_TARGETS=native

# Optional: Logging
# LOG_LEVEL=info

# Optional: CORS Configuration
# CORS_ORIGIN=http://localhost:3000

# Optional: Rate Limiting
# RATE_LIMIT_TTL=60
# RATE_LIMIT_LIMIT=100
