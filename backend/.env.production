# Production Environment Configuration
# Copy this file to .env.prod and fill in your production values

# Database Configuration
POSTGRES_DB=scapartmentdb
POSTGRES_USER=postgres
POSTGRES_PASSWORD=CHANGE_THIS_SECURE_PASSWORD

# JWT Configuration - MUST CHANGE IN PRODUCTION
JWT_SECRET=CHANGE_THIS_TO_A_VERY_SECURE_RANDOM_STRING_AT_LEAST_32_CHARACTERS
JWT_EXPIRES_IN=7d

# Application Configuration
NODE_ENV=production
BACKEND_PORT=3001

# Optional: Additional Security
# CORS_ORIGIN=https://yourdomain.com
# RATE_LIMIT_TTL=60
# RATE_LIMIT_LIMIT=100

# Optional: Logging
# LOG_LEVEL=warn
