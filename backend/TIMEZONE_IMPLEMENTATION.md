# Backend UTC+7 Timezone Implementation

This document explains how the backend application handles timezone conversion to ensure all dates and times are consistently processed in UTC+7 timezone.

## Overview

The backend has been updated to handle all date operations in UTC+7 timezone (Asia/Bangkok) while maintaining UTC storage in the database. This ensures consistency across all users and proper timezone handling for the application's target region.

## Implementation Details

### Core Service (`src/common/timezone.service.ts`)

A comprehensive `TimezoneService` has been created to handle all timezone operations:

#### Main Functions

- **`toUTC7(date)`** - Converts any date to UTC+7 timezone
- **`getCurrentDateUTC7()`** - Gets current date/time in UTC+7
- **`parseToUTC7(dateString)`** - Parses date strings to UTC+7 Date objects
- **`isFutureDate(date)`** - Checks if date is in future (UTC+7 context)
- **`getMonthRange(year, month)`** - Gets start/end dates for month filtering
- **`formatForDatabase(date)`** - Formats dates for database storage

#### Utility Functions

- **`isToday(date)`** - Checks if date is today in UTC+7
- **`getStartOfDay(date)`** / **`getEndOfDay(date)`** - Day boundaries in UTC+7
- **`compareDates(date1, date2)`** - Compare dates in UTC+7 context
- **`addDays(date, days)`** / **`subtractDays(date, days)`** - Date arithmetic
- **`getDaysDifference(date1, date2)`** - Calculate day differences

### Global Module (`src/common/common.module.ts`)

The `CommonModule` is configured as a global module, making `TimezoneService` available throughout the application without explicit imports in each module.

## Updated Services

### 1. BillingService (`src/billing/billing.service.ts`)

**Changes Made:**
- **Billing Date Creation**: Uses `parseToUTC7()` for billing date processing
- **Room Updates**: Uses `getCurrentDateUTC7()` for updatedAt timestamps
- **Month Range Filtering**: Uses `getMonthRange()` for accurate month-based queries
- **Payment Date Validation**: Uses `isFutureDate()` for UTC+7 context validation
- **Payment Entry Creation**: Stores payment dates in UTC+7 context

**Before:**
```typescript
billingDate: new Date(createBillingDto.billingDate)
updatedAt: new Date()
if (paymentDate > new Date()) // validation
```

**After:**
```typescript
billingDate: this.timezoneService.parseToUTC7(createBillingDto.billingDate)
updatedAt: this.timezoneService.getCurrentDateUTC7()
if (this.timezoneService.isFutureDate(utc7PaymentDate)) // validation
```

### 2. BillingController (`src/billing/billing.controller.ts`)

**Changes Made:**
- **Payment Date Processing**: Converts payment date strings to UTC+7 before passing to service

**Before:**
```typescript
new Date(createPaymentDto.paymentDate)
```

**After:**
```typescript
this.timezoneService.parseToUTC7(createPaymentDto.paymentDate)
```

## Database Considerations

### Storage Strategy
- **Database Storage**: Dates are still stored in UTC in the database for consistency
- **Processing Layer**: All business logic operates in UTC+7 timezone
- **API Layer**: Date strings from frontend are converted to UTC+7 for processing

### Migration Impact
- **No Database Changes**: Existing data remains in UTC format
- **Backward Compatibility**: All existing dates are properly converted to UTC+7 for display
- **Data Integrity**: No data loss or corruption during timezone implementation

## API Endpoints

### Date Handling in Controllers

All date inputs from the frontend are now processed through the timezone service:

1. **Billing Entry Creation**: Billing dates converted to UTC+7
2. **Payment Entry Creation**: Payment dates converted to UTC+7
3. **Date Range Queries**: Month/year filters use UTC+7 boundaries

### Request/Response Format

- **Input**: Date strings in ISO format (from frontend)
- **Processing**: Converted to UTC+7 for business logic
- **Storage**: Stored in UTC in database
- **Output**: Returned as UTC dates (frontend handles display conversion)

## Configuration

### Module Setup

```typescript
// app.module.ts
@Module({
  imports: [
    CommonModule, // Global timezone service
    // ... other modules
  ],
})
export class AppModule {}
```

### Service Injection

```typescript
// Any service that needs timezone handling
constructor(
  private prisma: PrismaService,
  private timezoneService: TimezoneService, // Automatically available
) {}
```

## Usage Examples

### Creating Billing Entries

```typescript
// Convert frontend date string to UTC+7
const billingDate = this.timezoneService.parseToUTC7(dto.billingDate);

// Create entry with UTC+7 date
const entry = await this.prisma.billingEntry.create({
  data: {
    billingDate,
    // ... other fields
  },
});
```

### Date Validation

```typescript
// Validate payment date is not in future (UTC+7 context)
if (this.timezoneService.isFutureDate(paymentDate)) {
  throw new BadRequestException('Payment date cannot be in the future');
}
```

### Month Range Queries

```typescript
// Get accurate month boundaries in UTC+7
const { startDate, endDate } = this.timezoneService.getMonthRange(year, month);

// Use in database queries
const entries = await this.prisma.billingEntry.findMany({
  where: {
    billingDate: {
      gte: startDate,
      lte: endDate,
    },
  },
});
```

## Testing

### Manual Testing

1. **Create Billing Entries**: Verify dates are processed correctly
2. **Add Payments**: Check payment date validation and storage
3. **Filter by Month**: Ensure month ranges work correctly in UTC+7
4. **Cross-Timezone Testing**: Test from different browser timezones

### Expected Behavior

- **Consistent Processing**: All date operations use UTC+7 context
- **Accurate Validation**: Future date checks work correctly
- **Proper Filtering**: Month-based queries return correct results
- **Data Integrity**: Dates are stored consistently

## Benefits

1. **Consistency**: All backend date operations use UTC+7 timezone
2. **Accuracy**: Business logic operates in the correct timezone context
3. **Maintainability**: Centralized timezone handling through service
4. **Scalability**: Easy to extend for multiple timezone support
5. **Reliability**: Proper validation and date arithmetic in UTC+7

## Troubleshooting

### Common Issues

1. **Incorrect Date Ranges**: Ensure using `getMonthRange()` for month-based queries
2. **Validation Failures**: Use `isFutureDate()` instead of direct date comparison
3. **Date Arithmetic**: Use timezone service methods for adding/subtracting days

### Debugging

```typescript
// Check timezone conversion
console.log('Timezone Info:', this.timezoneService.getTimezoneInfo());

// Verify date conversion
const inputDate = '2024-01-15';
const utc7Date = this.timezoneService.parseToUTC7(inputDate);
console.log('Converted to UTC+7:', utc7Date);
```

## Future Enhancements

1. **Multiple Timezone Support**: Framework supports adding other timezones
2. **User Preferences**: Could allow users to choose their preferred timezone
3. **Audit Logging**: All date operations are timezone-aware
4. **Reporting**: Date-based reports use consistent timezone
