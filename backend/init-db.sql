-- Initialize database for Apartment Management System
-- This script runs when the PostgreSQL container starts for the first time

-- Create database if it doesn't exist (handled by POSTGRES_DB environment variable)
-- The database 'scapartmentdb' will be created automatically

-- Set timezone to UTC+7 (Bangkok/Asia timezone)
SET timezone = 'Asia/Bangkok';

-- Create extensions if needed
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Log initialization
DO $$
BEGIN
    RAISE NOTICE 'Database initialized successfully for Apartment Management System';
    RAISE NOTICE 'Timezone set to: %', current_setting('timezone');
    RAISE NOTICE 'Current timestamp: %', NOW();
END $$;
