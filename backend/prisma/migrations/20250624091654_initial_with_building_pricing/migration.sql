-- Create<PERSON><PERSON>
CREATE TYPE "role" AS ENUM ('ADMIN', 'USER');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "room_status" AS ENUM ('AVAILABLE', 'OCCUPIED', 'MAINTENANCE');

-- CreateTable
CREATE TABLE "users" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "role" "role" NOT NULL DEFAULT 'USER',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "buildings" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "created_by" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "buildings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "rooms" (
    "id" TEXT NOT NULL,
    "building_id" TEXT NOT NULL,
    "room_number" TEXT NOT NULL,
    "monthly_rent" DECIMAL(10,2) NOT NULL DEFAULT 0,
    "initial_water_reading" DECIMAL(10,2) NOT NULL DEFAULT 0,
    "initial_electricity_reading" DECIMAL(10,2) NOT NULL DEFAULT 0,
    "current_water_reading" DECIMAL(10,2) NOT NULL DEFAULT 0,
    "current_electricity_reading" DECIMAL(10,2) NOT NULL DEFAULT 0,
    "status" "room_status" NOT NULL DEFAULT 'AVAILABLE',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "rooms_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "additional_services" (
    "id" TEXT NOT NULL,
    "room_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "price" DECIMAL(10,2) NOT NULL DEFAULT 0,
    "description" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "additional_services_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "billing_entries" (
    "id" TEXT NOT NULL,
    "room_id" TEXT NOT NULL,
    "billing_date" DATE NOT NULL,
    "water_reading" DECIMAL(10,2) NOT NULL,
    "electricity_reading" DECIMAL(10,2) NOT NULL,
    "water_consumption" DECIMAL(10,2) NOT NULL,
    "electricity_consumption" DECIMAL(10,2) NOT NULL,
    "water_cost" DECIMAL(10,2) NOT NULL,
    "electricity_cost" DECIMAL(10,2) NOT NULL,
    "additional_services_cost" DECIMAL(10,2) NOT NULL DEFAULT 0,
    "total_amount" DECIMAL(10,2) NOT NULL,
    "created_by" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "billing_entries_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "building_pricing" (
    "id" TEXT NOT NULL,
    "building_id" TEXT NOT NULL,
    "water_price_per_unit" DECIMAL(10,4) NOT NULL DEFAULT 0,
    "electricity_price_per_unit" DECIMAL(10,4) NOT NULL DEFAULT 0,
    "billing_enabled" BOOLEAN NOT NULL DEFAULT false,
    "updated_by" TEXT NOT NULL,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "building_pricing_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "rooms_building_id_room_number_key" ON "rooms"("building_id", "room_number");

-- CreateIndex
CREATE UNIQUE INDEX "building_pricing_building_id_key" ON "building_pricing"("building_id");

-- AddForeignKey
ALTER TABLE "buildings" ADD CONSTRAINT "buildings_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "rooms" ADD CONSTRAINT "rooms_building_id_fkey" FOREIGN KEY ("building_id") REFERENCES "buildings"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "additional_services" ADD CONSTRAINT "additional_services_room_id_fkey" FOREIGN KEY ("room_id") REFERENCES "rooms"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "billing_entries" ADD CONSTRAINT "billing_entries_room_id_fkey" FOREIGN KEY ("room_id") REFERENCES "rooms"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "billing_entries" ADD CONSTRAINT "billing_entries_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "building_pricing" ADD CONSTRAINT "building_pricing_building_id_fkey" FOREIGN KEY ("building_id") REFERENCES "buildings"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "building_pricing" ADD CONSTRAINT "building_pricing_updated_by_fkey" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
