/*
  Warnings:

  - A unique constraint covering the columns `[username]` on the table `users` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `username` to the `users` table without a default value. This is not possible if the table is not empty.

*/
-- Step 1: Add username column as nullable first
ALTER TABLE "users" ADD COLUMN "username" TEXT;

-- Step 2: Generate unique usernames for existing users based on email
UPDATE "users" SET "username" = CONCAT('user_', SUBSTRING(MD5(email), 1, 8)) WHERE "username" IS NULL;

-- Step 3: Make username column NOT NULL
ALTER TABLE "users" ALTER COLUMN "username" SET NOT NULL;

-- CreateTable
CREATE TABLE "user_building_access" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "building_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "user_building_access_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "user_building_access_user_id_building_id_key" ON "user_building_access"("user_id", "building_id");

-- CreateIndex
CREATE UNIQUE INDEX "users_username_key" ON "users"("username");

-- AddForeignKey
ALTER TABLE "user_building_access" ADD CONSTRAINT "user_building_access_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_building_access" ADD CONSTRAINT "user_building_access_building_id_fkey" FOREIGN KEY ("building_id") REFERENCES "buildings"("id") ON DELETE CASCADE ON UPDATE CASCADE;
