/*
  Warnings:

  - Added the required column `initial_electricity_reading` to the `billing_entries` table without a default value. This is not possible if the table is not empty.
  - Added the required column `initial_water_reading` to the `billing_entries` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "billing_status" AS ENUM ('UNPAID', 'PAID', 'PARTIALLY_PAID');

-- AlterTable: Add columns with default values first
ALTER TABLE "billing_entries" ADD COLUMN     "initial_electricity_reading" DECIMAL(10,2) DEFAULT 0,
ADD COLUMN     "initial_water_reading" DECIMAL(10,2) DEFAULT 0,
ADD COLUMN     "status" "billing_status" NOT NULL DEFAULT 'UNPAID';

-- Update existing records to set initial readings to 0 (or you can set them to current readings if preferred)
UPDATE "billing_entries" SET
  "initial_electricity_reading" = 0,
  "initial_water_reading" = 0
WHERE "initial_electricity_reading" IS NULL OR "initial_water_reading" IS NULL;

-- Now make the columns NOT NULL
ALTER TABLE "billing_entries" ALTER COLUMN "initial_electricity_reading" SET NOT NULL;
ALTER TABLE "billing_entries" ALTER COLUMN "initial_water_reading" SET NOT NULL;
