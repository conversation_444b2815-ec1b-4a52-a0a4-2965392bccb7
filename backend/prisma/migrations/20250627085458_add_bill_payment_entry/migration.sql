-- CreateTable
CREATE TABLE "bill_payment_entries" (
    "id" TEXT NOT NULL,
    "billing_id" TEXT NOT NULL,
    "amount" DECIMAL(10,2) NOT NULL,
    "payment_date" DATE NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "bill_payment_entries_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "bill_payment_entries_billing_id_payment_date_key" ON "bill_payment_entries"("billing_id", "payment_date");

-- AddForeignKey
ALTER TABLE "bill_payment_entries" ADD CONSTRAINT "bill_payment_entries_billing_id_fkey" FOREIGN KEY ("billing_id") REFERENCES "billing_entries"("id") ON DELETE CASCADE ON UPDATE CASCADE;
