-- CreateTable
CREATE TABLE "customers" (
    "id" TEXT NOT NULL,
    "name_en" TEXT NOT NULL,
    "name_th" TEXT,
    "id_number" TEXT NOT NULL,
    "phone_number" TEXT NOT NULL,
    "address" TEXT,
    "building_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "customers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "customer_room_assignments" (
    "id" TEXT NOT NULL,
    "customer_id" TEXT NOT NULL,
    "room_id" TEXT NOT NULL,
    "assigned_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "customer_room_assignments_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "customers_id_number_building_id_key" ON "customers"("id_number", "building_id");

-- CreateIndex
CREATE UNIQUE INDEX "customer_room_assignments_customer_id_room_id_key" ON "customer_room_assignments"("customer_id", "room_id");

-- AddForeignKey
ALTER TABLE "customers" ADD CONSTRAINT "customers_building_id_fkey" FOREIGN KEY ("building_id") REFERENCES "buildings"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "customer_room_assignments" ADD CONSTRAINT "customer_room_assignments_customer_id_fkey" FOREIGN KEY ("customer_id") REFERENCES "customers"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "customer_room_assignments" ADD CONSTRAINT "customer_room_assignments_room_id_fkey" FOREIGN KEY ("room_id") REFERENCES "rooms"("id") ON DELETE CASCADE ON UPDATE CASCADE;
