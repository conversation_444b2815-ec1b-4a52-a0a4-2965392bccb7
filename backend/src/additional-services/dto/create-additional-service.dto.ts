import {
  <PERSON><PERSON><PERSON>,
  <PERSON>NotEmpty,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class CreateAdditionalServiceDto {
  @ApiProperty({ example: 'Internet Service' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ example: 50.0 })
  @IsNumber()
  @Min(0)
  @Transform(({ value }) => parseFloat(value))
  price: number;

  @ApiProperty({ example: 'High-speed internet connection', required: false })
  @IsOptional()
  @IsString()
  description?: string;
}
