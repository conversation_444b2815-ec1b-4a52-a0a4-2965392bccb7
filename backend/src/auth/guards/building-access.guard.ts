import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
} from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';

@Injectable()
export class BuildingAccessGuard implements CanActivate {
  constructor(private prisma: PrismaService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;
    const buildingId = request.params.buildingId || request.params.id;

    if (!user) {
      return false;
    }

    // Admin users have access to all buildings they created
    if (user.role === 'ADMIN') {
      const building = await this.prisma.building.findUnique({
        where: { id: buildingId },
        select: { createdBy: true },
      });

      if (!building) {
        throw new ForbiddenException('Building not found');
      }

      return building.createdBy === user.id;
    }

    // USER role needs explicit building access
    if (user.role === 'USER') {
      const access = await this.prisma.userBuildingAccess.findUnique({
        where: {
          userId_buildingId: {
            userId: user.id,
            buildingId: buildingId,
          },
        },
      });

      return !!access;
    }

    return false;
  }
}
