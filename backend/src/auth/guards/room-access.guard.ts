import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
} from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';

@Injectable()
export class RoomAccessGuard implements CanActivate {
  constructor(private prisma: PrismaService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;
    const roomId = request.params.roomId || request.params.id;

    if (!user) {
      return false;
    }

    // Get room with building information
    const room = await this.prisma.room.findUnique({
      where: { id: roomId },
      include: {
        building: {
          select: {
            id: true,
            createdBy: true,
          },
        },
      },
    });

    if (!room) {
      throw new ForbiddenException('Room not found');
    }

    // Admin users have access to rooms in buildings they created
    if (user.role === 'ADMIN') {
      return room.building.createdBy === user.id;
    }

    // USER role needs explicit building access
    if (user.role === 'USER') {
      const access = await this.prisma.userBuildingAccess.findUnique({
        where: {
          userId_buildingId: {
            userId: user.id,
            buildingId: room.building.id,
          },
        },
      });

      return !!access;
    }

    return false;
  }
}
