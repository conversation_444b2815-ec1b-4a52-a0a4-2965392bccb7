import {
  Controller,
  Post,
  Get,
  Delete,
  Body,
  Param,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { UserManagementService } from './user-management.service';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { RolesGuard } from './guards/roles.guard';
import { Roles } from './decorators/roles.decorator';
import { User } from './decorators/user.decorator';
import { Role } from '@prisma/client';

@ApiTags('User Management')
@Controller('user-management')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class UserManagementController {
  constructor(private readonly userManagementService: UserManagementService) {}

  @Post('buildings/:buildingId/users')
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: 'Create a new USER account for a building' })
  @ApiResponse({ status: 201, description: 'User created successfully' })
  @ApiResponse({ status: 403, description: 'Access denied' })
  @ApiResponse({ status: 404, description: 'Building not found' })
  async createUserForBuilding(
    @Param('buildingId') buildingId: string,
    @Body() body: { customPassword?: string; customUsername?: string },
    @User() user: any,
  ) {
    console.log('Controller received body:', body);
    return this.userManagementService.createUserWithBuildingAccess(
      buildingId,
      user.id,
      body.customPassword,
      body.customUsername,
    );
  }

  @Get('buildings/:buildingId/users')
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: 'Get users assigned to a building' })
  @ApiResponse({ status: 200, description: 'Users retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Access denied' })
  @ApiResponse({ status: 404, description: 'Building not found' })
  async getBuildingUsers(
    @Param('buildingId') buildingId: string,
    @User() user: any,
  ) {
    return this.userManagementService.getBuildingUsers(buildingId, user.id);
  }

  @Delete('buildings/:buildingId/users/:userId')
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: 'Remove user access from a building' })
  @ApiResponse({ status: 200, description: 'User access removed successfully' })
  @ApiResponse({ status: 403, description: 'Access denied' })
  @ApiResponse({ status: 404, description: 'Building or user not found' })
  async removeUserFromBuilding(
    @Param('buildingId') buildingId: string,
    @Param('userId') userId: string,
    @User() user: any,
  ) {
    return this.userManagementService.removeUserFromBuilding(
      buildingId,
      userId,
      user.id,
    );
  }

  @Post('buildings/:buildingId/add-user')
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: 'Add existing user to building by username' })
  @ApiResponse({
    status: 201,
    description: 'User added to building successfully',
  })
  @ApiResponse({ status: 403, description: 'Access denied' })
  @ApiResponse({ status: 404, description: 'Building or user not found' })
  @ApiResponse({
    status: 409,
    description: 'User already has access to building',
  })
  async addUserToBuilding(
    @Param('buildingId') buildingId: string,
    @Body() body: { username: string },
    @User() user: any,
  ) {
    return this.userManagementService.addUserToBuilding(
      buildingId,
      body.username,
      user.id,
    );
  }

  @Get('my-buildings')
  @Roles(Role.USER)
  @ApiOperation({ summary: 'Get buildings accessible to the current user' })
  @ApiResponse({ status: 200, description: 'Buildings retrieved successfully' })
  async getMyBuildings(@User() user: any) {
    return this.userManagementService.getUserAccessibleBuildings(user.id);
  }
}
