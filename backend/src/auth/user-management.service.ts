import {
  Injectable,
  ConflictException,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import * as bcrypt from 'bcryptjs';

@Injectable()
export class UserManagementService {
  constructor(private prisma: PrismaService) {}

  // Generate random username
  private generateRandomUsername(): string {
    const adjectives = [
      'quick',
      'bright',
      'calm',
      'bold',
      'wise',
      'kind',
      'smart',
      'cool',
    ];
    const nouns = ['user', 'admin', 'manager', 'tenant', 'owner', 'guest'];
    const randomNum = Math.floor(Math.random() * 1000);

    const adjective = adjectives[Math.floor(Math.random() * adjectives.length)];
    const noun = nouns[Math.floor(Math.random() * nouns.length)];

    return `${adjective}_${noun}_${randomNum}`;
  }

  // Generate random password
  private generateRandomPassword(): string {
    const chars =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let password = '';
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password;
  }

  // Create a new USER account with building access
  async createUserWithBuildingAccess(
    buildingId: string,
    adminUserId: string,
    customPassword?: string,
    customUsername?: string,
  ) {
    console.log('Service received:', {
      buildingId,
      adminUserId,
      customPassword,
      customUsername,
    });
    // Verify admin has access to the building
    const building = await this.prisma.building.findUnique({
      where: { id: buildingId },
      select: { id: true, name: true, createdBy: true },
    });

    if (!building) {
      throw new NotFoundException('Building not found');
    }

    if (building.createdBy !== adminUserId) {
      throw new ForbiddenException(
        'You can only create users for buildings you own',
      );
    }

    // Generate credentials
    let username: string;

    if (customUsername) {
      // Check if custom username is already taken
      const existingUser = await this.prisma.user.findUnique({
        where: { username: customUsername },
      });

      if (existingUser) {
        throw new ConflictException('Username is already taken');
      }

      username = customUsername;
    } else {
      // Generate random username
      username = this.generateRandomUsername();

      // Ensure username is unique
      let existingUser = await this.prisma.user.findUnique({
        where: { username },
      });
      while (existingUser) {
        username = this.generateRandomUsername();
        existingUser = await this.prisma.user.findUnique({
          where: { username },
        });
      }
    }

    const password = customPassword || this.generateRandomPassword();
    const email = `${username}@temp.local`; // Temporary email format

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create user with USER role
    const user = await this.prisma.user.create({
      data: {
        email,
        username,
        password: hashedPassword,
        role: 'USER',
      },
      select: {
        id: true,
        email: true,
        username: true,
        role: true,
        createdAt: true,
      },
    });

    // Create building access
    await this.prisma.userBuildingAccess.create({
      data: {
        userId: user.id,
        buildingId: buildingId,
      },
    });

    return {
      user,
      credentials: {
        username,
        password, // Return plain password for admin to share
      },
    };
  }

  // Get users assigned to a building
  async getBuildingUsers(buildingId: string, adminUserId: string) {
    // Verify admin has access to the building
    const building = await this.prisma.building.findUnique({
      where: { id: buildingId },
      select: { id: true, name: true, createdBy: true },
    });

    if (!building) {
      throw new NotFoundException('Building not found');
    }

    if (building.createdBy !== adminUserId) {
      throw new ForbiddenException(
        'You can only view users for buildings you own',
      );
    }

    // Get users with access to this building
    const buildingAccess = await this.prisma.userBuildingAccess.findMany({
      where: { buildingId },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            username: true,
            role: true,
            createdAt: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return {
      building,
      users: buildingAccess.map((access) => ({
        ...access.user,
        accessGrantedAt: access.createdAt,
      })),
    };
  }

  // Remove user from building
  async removeUserFromBuilding(
    buildingId: string,
    userId: string,
    adminUserId: string,
  ) {
    // Verify admin has access to the building
    const building = await this.prisma.building.findUnique({
      where: { id: buildingId },
      select: { id: true, name: true, createdBy: true },
    });

    if (!building) {
      throw new NotFoundException('Building not found');
    }

    if (building.createdBy !== adminUserId) {
      throw new ForbiddenException(
        'You can only manage users for buildings you own',
      );
    }

    // Check if user has access to this building
    const access = await this.prisma.userBuildingAccess.findUnique({
      where: {
        userId_buildingId: {
          userId,
          buildingId,
        },
      },
    });

    if (!access) {
      throw new NotFoundException('User does not have access to this building');
    }

    // Remove access
    await this.prisma.userBuildingAccess.delete({
      where: {
        userId_buildingId: {
          userId,
          buildingId,
        },
      },
    });

    return { message: 'User access removed successfully' };
  }

  // Add existing user to building by username
  async addUserToBuilding(
    buildingId: string,
    username: string,
    adminUserId: string,
  ) {
    // Verify admin has access to the building
    const building = await this.prisma.building.findUnique({
      where: { id: buildingId },
      select: { id: true, name: true, createdBy: true },
    });

    if (!building) {
      throw new NotFoundException('Building not found');
    }

    if (building.createdBy !== adminUserId) {
      throw new ForbiddenException(
        'You can only manage users for buildings you own',
      );
    }

    // Check if user exists
    const user = await this.prisma.user.findUnique({
      where: { username },
      select: {
        id: true,
        email: true,
        username: true,
        role: true,
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Check if user already has access to this building
    const existingAccess = await this.prisma.userBuildingAccess.findUnique({
      where: {
        userId_buildingId: {
          userId: user.id,
          buildingId,
        },
      },
    });

    if (existingAccess) {
      throw new ConflictException('User already has access to this building');
    }

    // Create building access
    await this.prisma.userBuildingAccess.create({
      data: {
        userId: user.id,
        buildingId,
      },
    });

    return {
      message: 'User successfully added to building',
      user,
      building,
    };
  }

  // Get buildings accessible to a user (for USER role dashboard)
  async getUserAccessibleBuildings(userId: string) {
    const buildingAccess = await this.prisma.userBuildingAccess.findMany({
      where: { userId },
      include: {
        building: {
          include: {
            rooms: {
              select: {
                id: true,
                roomNumber: true,
                status: true,
                monthlyRent: true,
              },
            },
            _count: {
              select: {
                rooms: true,
              },
            },
          },
        },
      },
    });

    return buildingAccess.map((access) => access.building);
  }
}
