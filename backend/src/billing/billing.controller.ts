import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Query,
  UseGuards,
  ForbiddenException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { BillingService } from './billing.service';
import { CreateBillingDto } from './dto/create-billing.dto';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { UpdateBuildingPricingDto } from './dto/update-building-pricing.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RoomAccessGuard } from '../auth/guards/room-access.guard';
import { BuildingAccessGuard } from '../auth/guards/building-access.guard';
import { User } from '../auth/decorators/user.decorator';
import { TimezoneService } from '../common/timezone.service';
import { RolesGuard } from 'src/auth/guards/roles.guard';
import { Roles } from 'src/auth/decorators/roles.decorator';

@ApiTags('Billing')
@Controller('billing')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class BillingController {
  constructor(
    private readonly billingService: BillingService,
    private readonly timezoneService: TimezoneService,
  ) {}

  @Post('rooms/:roomId/entries')
  @UseGuards(RoomAccessGuard)
  @ApiOperation({ summary: 'Create a billing entry for a room' })
  @ApiResponse({
    status: 201,
    description: 'Billing entry created successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Billing not enabled or invalid data',
  })
  createBillingEntry(
    @Param('roomId') roomId: string,
    @Body() createBillingDto: CreateBillingDto,
    @User() user: any,
  ) {
    return this.billingService.createBillingEntry(
      roomId,
      createBillingDto,
      user.id,
    );
  }

  @Get('entries')
  @ApiOperation({ summary: 'Get billing entries' })
  @ApiQuery({
    name: 'roomId',
    required: false,
    description: 'Filter by room ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Billing entries retrieved successfully',
  })
  async findBillingEntries(
    @Query('roomId') roomId?: string,
    @User() user?: any,
  ) {
    // If roomId is provided, check room access manually since we can't use conditional guards
    if (roomId) {
      const room = await this.billingService.checkRoomAccess(
        roomId,
        user.id,
        user.role,
      );
      if (!room) {
        throw new ForbiddenException('Access denied');
      }
    }

    return this.billingService.findBillingEntries(roomId);
  }

  @Get('buildings/:buildingId/pricing')
  @UseGuards(BuildingAccessGuard)
  @ApiOperation({ summary: 'Get building-specific pricing' })
  @ApiResponse({
    status: 200,
    description: 'Building pricing retrieved successfully',
  })
  @ApiResponse({ status: 404, description: 'Building not found' })
  @ApiResponse({ status: 403, description: 'Access denied' })
  async getBuildingPricing(@Param('buildingId') buildingId: string) {
    return await this.billingService.getBuildingPricing(buildingId);
  }

  @Patch('buildings/:buildingId/pricing')
  @UseGuards(BuildingAccessGuard)
  @ApiOperation({ summary: 'Update building-specific pricing' })
  @ApiResponse({
    status: 200,
    description: 'Building pricing updated successfully',
  })
  @ApiResponse({ status: 404, description: 'Building not found' })
  @ApiResponse({ status: 403, description: 'Access denied' })
  updateBuildingPricing(
    @Param('buildingId') buildingId: string,
    @Body() updateBuildingPricingDto: UpdateBuildingPricingDto,
    @User() user: any,
  ) {
    return this.billingService.updateBuildingPricing(
      buildingId,
      updateBuildingPricingDto,
      user.id,
      user.role,
    );
  }

  @Get('buildings/:buildingId/toggle-validation')
  @UseGuards(BuildingAccessGuard)
  @ApiOperation({ summary: 'Get billing toggle validation status' })
  @ApiResponse({
    status: 200,
    description: 'Billing toggle validation status retrieved successfully',
  })
  @ApiResponse({ status: 404, description: 'Building not found' })
  @ApiResponse({ status: 403, description: 'Access denied' })
  getBillingToggleValidation(@Param('buildingId') buildingId: string) {
    return this.billingService.getBillingToggleValidation(buildingId);
  }

  @Get('buildings/:buildingId/current-cycle')
  @UseGuards(BuildingAccessGuard)
  @ApiOperation({ summary: 'Get billing toggle validation status' })
  @ApiResponse({
    status: 200,
    description: 'Billing toggle validation status retrieved successfully',
  })
  @ApiResponse({ status: 404, description: 'Building not found' })
  @ApiResponse({ status: 403, description: 'Access denied' })
  getCurrentBillingCycle(@Param('buildingId') buildingId: string) {
    return this.billingService.getCurrentBillingCycle(buildingId);
  }

  @Get('/buildings/:buildingId/cycles')
  @Roles('ADMIN')
  @UseGuards(RolesGuard, BuildingAccessGuard)
  @ApiOperation({ summary: 'Get billing history by month and year' })
  @ApiResponse({
    status: 200,
    description: 'Billing history retrieved successfully',
  })
  @ApiResponse({ status: 400, description: 'Invalid month or year' })
  async getBillingCycles(@Param('buildingId') buildingId: string) {
    return this.billingService.getBillingCycles(buildingId);
  }

  @Get('/buildings/:buildingId/cycle/:cycleId')
  @Roles('ADMIN')
  @UseGuards(RolesGuard, BuildingAccessGuard)
  @ApiOperation({ summary: 'Get billing history by month and year' })
  @ApiQuery({ name: 'month', required: true, description: 'Month (1-12)' })
  @ApiQuery({ name: 'year', required: true, description: 'Year (YYYY)' })
  @ApiResponse({
    status: 200,
    description: 'Billing history retrieved successfully',
  })
  @ApiResponse({ status: 400, description: 'Invalid month or year' })
  async getBillingHistoryByCycle(@Param('cycleId') cycleId: string) {
    return this.billingService.getBillingHistoryByCycle(cycleId);
  }

  @Post(':billingId/payments')
  @ApiOperation({ summary: 'Add payment entry to billing (ADMIN only)' })
  @ApiResponse({
    status: 201,
    description: 'Payment entry created successfully',
  })
  @ApiResponse({ status: 400, description: 'Invalid payment data' })
  @ApiResponse({ status: 403, description: 'Access denied - ADMIN only' })
  @ApiResponse({ status: 404, description: 'Billing entry not found' })
  async addPaymentEntry(
    @Param('billingId') billingId: string,
    @Body() createPaymentDto: CreatePaymentDto,
    @User() user: any,
  ) {
    // Only ADMIN users can add payments
    if (user.role !== 'ADMIN') {
      throw new ForbiddenException('Only ADMIN users can add payment entries');
    }

    return this.billingService.addPaymentEntry(
      billingId,
      createPaymentDto.amount,
      this.timezoneService.parseToUTC7(createPaymentDto.paymentDate),
      createPaymentDto.paymentMethodName,
    );
  }

  @Get(':billingId/payments')
  @ApiOperation({ summary: 'Get payment history for a billing entry' })
  @ApiResponse({
    status: 200,
    description: 'Payment history retrieved successfully',
  })
  @ApiResponse({ status: 404, description: 'Billing entry not found' })
  async getPaymentHistory(@Param('billingId') billingId: string) {
    return this.billingService.getPaymentHistory(billingId);
  }
}
