import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsNumber, IsString, Min } from 'class-validator';
import { Transform } from 'class-transformer';

export class CreatePaymentDto {
  @ApiProperty({ example: 500.0 })
  @IsNumber()
  @Min(0.01, { message: 'Payment amount must be greater than 0' })
  @Transform(({ value }) => parseFloat(value))
  amount: number;

  @ApiProperty({ example: '2024-01-15' })
  @IsDateString()
  paymentDate: string;

  @ApiProperty({ example: 'Cash' })
  @IsString()
  paymentMethodName: string;
}
