import {
  IsN<PERSON>ber,
  IsBoolean,
  IsO<PERSON>al,
  Min,
  IsString,
  IsNotEmpty,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class UpdateBuildingPricingDto {
  @ApiProperty({ example: 15.5, required: false })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Transform(({ value }) => (value ? parseFloat(value) : undefined))
  waterPricePerUnit?: number;

  @ApiProperty({ example: 4.25, required: false })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Transform(({ value }) => (value ? parseFloat(value) : undefined))
  electricityPricePerUnit?: number;

  @ApiProperty({ example: true, required: false })
  @IsOptional()
  @IsBoolean()
  billingEnabled?: boolean;

  @ApiProperty({ example: '2025-07-08T11:49:51.838Z', required: false })
  @IsString()
  @IsNotEmpty()
  billingEnabledDate: string;

  @ApiProperty({ example: true, required: false })
  @IsOptional()
  @IsBoolean()
  isBillingEntryToggled?: boolean;
}
