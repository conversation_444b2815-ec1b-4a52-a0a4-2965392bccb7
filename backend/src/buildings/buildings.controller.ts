import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { BuildingsService } from './buildings.service';
import { CreateBuildingDto } from './dto/create-building.dto';
import { UpdateBuildingDto } from './dto/update-building.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { BuildingAccessGuard } from '../auth/guards/building-access.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { User } from '../auth/decorators/user.decorator';
import { Role } from '@prisma/client';

@ApiTags('Buildings')
@Controller('buildings')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class BuildingsController {
  constructor(private readonly buildingsService: BuildingsService) {}

  @Post()
  @Roles(Role.ADMIN)
  @UseGuards(RolesGuard)
  @ApiOperation({ summary: 'Create a new building' })
  @ApiResponse({ status: 201, description: 'Building successfully created' })
  create(@Body() createBuildingDto: CreateBuildingDto, @User() user: any) {
    return this.buildingsService.create(createBuildingDto, user.id);
  }

  @Get()
  @ApiOperation({ summary: 'Get all buildings' })
  @ApiResponse({ status: 200, description: 'Buildings retrieved successfully' })
  findAll(@User() user: any) {
    return this.buildingsService.findAll(user.id, user.role);
  }

  @Get(':id')
  @UseGuards(BuildingAccessGuard)
  @ApiOperation({ summary: 'Get a building by ID' })
  @ApiResponse({ status: 200, description: 'Building retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Building not found' })
  findOne(@Param('id') id: string, @User() user: any) {
    return this.buildingsService.findOne(id, user.id, user.role);
  }

  @Patch(':id')
  @Roles(Role.ADMIN)
  @UseGuards(RolesGuard, BuildingAccessGuard)
  @ApiOperation({ summary: 'Update a building' })
  @ApiResponse({ status: 200, description: 'Building updated successfully' })
  @ApiResponse({ status: 404, description: 'Building not found' })
  update(
    @Param('id') id: string,
    @Body() updateBuildingDto: UpdateBuildingDto,
    @User() user: any,
  ) {
    return this.buildingsService.update(
      id,
      updateBuildingDto,
      user.id,
      user.role,
    );
  }

  @Delete(':id')
  @Roles(Role.ADMIN)
  @UseGuards(RolesGuard, BuildingAccessGuard)
  @ApiOperation({ summary: 'Delete a building' })
  @ApiResponse({ status: 200, description: 'Building deleted successfully' })
  @ApiResponse({ status: 404, description: 'Building not found' })
  remove(@Param('id') id: string, @User() user: any) {
    return this.buildingsService.remove(id, user.id, user.role);
  }
}
