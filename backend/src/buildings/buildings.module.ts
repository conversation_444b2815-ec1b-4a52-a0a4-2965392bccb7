import { Module } from '@nestjs/common';
import { BuildingsService } from './buildings.service';
import { BuildingsController } from './buildings.controller';
import { PrismaModule } from '../prisma/prisma.module';
import { BuildingAccessGuard } from '../auth/guards/building-access.guard';

@Module({
  imports: [PrismaModule],
  controllers: [BuildingsController],
  providers: [BuildingsService, BuildingAccessGuard],
  exports: [BuildingsService],
})
export class BuildingsModule {}
