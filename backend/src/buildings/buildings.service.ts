import {
  Injectable,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateBuildingDto } from './dto/create-building.dto';
import { UpdateBuildingDto } from './dto/update-building.dto';
import { AccessBuildingDto } from './dto/access-building-dto';

@Injectable()
export class BuildingsService {
  constructor(private prisma: PrismaService) {}

  async create(createBuildingDto: CreateBuildingDto, userId: string) {
    return this.prisma.building.create({
      data: {
        ...createBuildingDto,
        createdBy: userId,
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            role: true,
          },
        },
        _count: {
          select: {
            rooms: true,
          },
        },
      },
    });
  }

  async findAll(userId: string, userRole: string) {
    if (userRole === 'ADMIN') {
      // <PERSON><PERSON> sees all buildings they created
      return this.prisma.building.findMany({
        where: { createdBy: userId },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              username: true,
              role: true,
            },
          },
          _count: {
            select: {
              rooms: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });
    } else {
      // USER sees only buildings they have access to
      const userAccess = await this.prisma.userBuildingAccess.findMany({
        where: { userId },
        include: {
          building: {
            include: {
              user: {
                select: {
                  id: true,
                  email: true,
                  username: true,
                  role: true,
                },
              },
              _count: {
                select: {
                  rooms: true,
                },
              },
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      return userAccess.map((access) => access.building);
    }
  }

  async canAccessBuilding(
    accessBuildingDto: AccessBuildingDto,
  ): Promise<boolean> {
    if (accessBuildingDto.userRole === 'ADMIN') {
      // Admin can only access buildings they created
      if (accessBuildingDto.buildingCreatedBy !== accessBuildingDto.userId) {
        return false;
      }
    } else if (accessBuildingDto.userRole === 'USER') {
      // USER needs explicit building access
      const access = await this.prisma.userBuildingAccess.findUnique({
        where: {
          userId_buildingId: {
            userId: accessBuildingDto.userId,
            buildingId: accessBuildingDto.buildingId,
          },
        },
      });

      if (!access) {
        return false;
      }
    } else {
      return false;
    }

    return true;
  }

  async findOne(id: string, userId: string, userRole: string) {
    const building = await this.prisma.building.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            username: true,
            role: true,
          },
        },
        rooms: {
          include: {
            additionalServices: true,
            _count: {
              select: {
                billingEntries: true,
              },
            },
          },
          orderBy: {
            roomNumber: 'asc',
          },
        },
      },
    });

    if (!building) {
      throw new NotFoundException('Building not found');
    }

    // Check if user has access to this building
    if (
      !(await this.canAccessBuilding(
        new AccessBuildingDto(userRole, building.createdBy, id, userId),
      ))
    ) {
      throw new ForbiddenException('Access denied');
    }

    return building;
  }

  async update(
    id: string,
    updateBuildingDto: UpdateBuildingDto,
    userId: string,
    userRole: string,
  ) {
    await this.findOne(id, userId, userRole);

    return this.prisma.building.update({
      where: { id },
      data: updateBuildingDto,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            role: true,
          },
        },
        _count: {
          select: {
            rooms: true,
          },
        },
      },
    });
  }

  async remove(id: string, userId: string, userRole: string) {
    // Verify building exists and user has permission to delete it
    await this.findOne(id, userId, userRole);

    await this.prisma.building.delete({
      where: { id },
    });

    return { message: 'Building deleted successfully' };
  }
}
