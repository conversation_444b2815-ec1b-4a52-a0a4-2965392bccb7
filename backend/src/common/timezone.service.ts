import { Injectable } from '@nestjs/common';

/**
 * Timezone Service for handling UTC+7 timezone conversions
 *
 * This service provides utilities to ensure all date operations
 * in the backend are consistent with UTC+7 timezone (Asia/Bangkok)
 */
@Injectable()
export class TimezoneService {
  private readonly UTC_PLUS_7_TIMEZONE = 'Asia/Bangkok';
  private readonly UTC_PLUS_7_OFFSET = 7 * 60 * 60 * 1000; // 7 hours in milliseconds

  /**
   * Convert any date to UTC+7 timezone
   */
  toUTC7(date: Date | string): Date {
    const inputDate = new Date(date);

    // Add 7 hours (UTC+7 offset) to the UTC time
    const utc7Date = new Date(inputDate.getTime() + this.UTC_PLUS_7_OFFSET);

    return utc7Date;
  }

  /**
   * Get current date and time in UTC+7 timezone
   */
  getCurrentDateUTC7(): Date {
    return this.toUTC7(new Date());
  }

  /**
   * Get current date in UTC+7 timezone (start of day)
   */
  getCurrentDateStartOfDayUTC7(): Date {
    const utc7Date = this.getCurrentDateUTC7();
    utc7Date.setHours(0, 0, 0, 0);
    return utc7Date;
  }

  /**
   * Get current date in UTC+7 timezone (end of day)
   */
  getCurrentDateEndOfDayUTC7(): Date {
    const utc7Date = this.getCurrentDateUTC7();
    utc7Date.setHours(23, 59, 59, 999);
    return utc7Date;
  }

  /**
   * Convert date string to UTC+7 Date object
   * Useful for processing date strings from frontend
   */
  parseToUTC7(dateString: string): Date {
    return this.toUTC7(new Date(dateString));
  }

  /**
   * Check if a date is in the future (UTC+7 context)
   */
  isFutureDate(date: Date | string): boolean {
    const inputDate = date;
    const currentDate = this.getCurrentDateUTC7();
    return inputDate > currentDate;
  }

  /**
   * Check if a date is today (UTC+7 context)
   */
  isToday(date: Date | string): boolean {
    const inputDate = this.toUTC7(date);
    const today = this.getCurrentDateUTC7();

    return (
      inputDate.getFullYear() === today.getFullYear() &&
      inputDate.getMonth() === today.getMonth() &&
      inputDate.getDate() === today.getDate()
    );
  }

  /**
   * Get start of day for a given date in UTC+7
   */
  getStartOfDay(date: Date | string): Date {
    const utc7Date = this.toUTC7(date);
    utc7Date.setHours(0, 0, 0, 0);
    return utc7Date;
  }

  /**
   * Get end of day for a given date in UTC+7
   */
  getEndOfDay(date: Date | string): Date {
    const utc7Date = this.toUTC7(date);
    utc7Date.setHours(23, 59, 59, 999);
    return utc7Date;
  }

  /**
   * Get start and end of month for filtering in UTC+7
   */
  getMonthRange(
    year: number,
    month: number,
  ): { startDate: Date; endDate: Date } {
    // Create dates in UTC+7 timezone
    const startDate = new Date();
    startDate.setFullYear(year, month - 1, 1);
    startDate.setHours(0, 0, 0, 0);

    const endDate = new Date();
    endDate.setFullYear(year, month, 0); // Last day of the month
    endDate.setHours(23, 59, 59, 999);

    return {
      startDate: this.toUTC7(startDate),
      endDate: this.toUTC7(endDate),
    };
  }

  /**
   * Format date for database storage (ISO string but considering UTC+7 context)
   */
  formatForDatabase(date: Date | string): Date {
    return this.toUTC7(date);
  }

  /**
   * Compare two dates in UTC+7 context
   * Returns: -1 if date1 < date2, 0 if equal, 1 if date1 > date2
   */
  compareDates(date1: Date | string, date2: Date | string): number {
    const utc7Date1 = this.toUTC7(date1);
    const utc7Date2 = this.toUTC7(date2);

    if (utc7Date1 < utc7Date2) return -1;
    if (utc7Date1 > utc7Date2) return 1;
    return 0;
  }

  /**
   * Add days to a date in UTC+7 context
   */
  addDays(date: Date | string, days: number): Date {
    const utc7Date = this.toUTC7(date);
    utc7Date.setDate(utc7Date.getDate() + days);
    return utc7Date;
  }

  /**
   * Subtract days from a date in UTC+7 context
   */
  subtractDays(date: Date | string, days: number): Date {
    return this.addDays(date, -days);
  }

  /**
   * Get the difference in days between two dates in UTC+7 context
   */
  getDaysDifference(date1: Date | string, date2: Date | string): number {
    const utc7Date1 = this.getStartOfDay(date1);
    const utc7Date2 = this.getStartOfDay(date2);

    const timeDifference = utc7Date1.getTime() - utc7Date2.getTime();
    return Math.ceil(timeDifference / (1000 * 60 * 60 * 24));
  }

  /**
   * Validate that a date string represents a valid date
   */
  isValidDate(dateString: string): boolean {
    const date = new Date(dateString);
    return !isNaN(date.getTime());
  }

  /**
   * Convert UTC date to UTC+7 for display purposes
   * This is useful when you have UTC dates from database
   */
  convertUTCToUTC7(utcDate: Date): Date {
    const utc7Date = new Date(utcDate.getTime() + this.UTC_PLUS_7_OFFSET);
    return utc7Date;
  }

  /**
   * Convert UTC+7 date to UTC for database storage
   * This ensures dates are stored consistently in UTC
   */
  convertUTC7ToUTC(utc7Date: Date): Date {
    const utcDate = new Date(utc7Date.getTime() - this.UTC_PLUS_7_OFFSET);
    return utcDate;
  }

  /**
   * Get timezone info for debugging
   */
  getTimezoneInfo(): {
    timezone: string;
    offset: number;
    currentUTC: string;
    currentUTC7: string;
  } {
    const now = new Date();
    const utc7Now = this.getCurrentDateUTC7();

    return {
      timezone: this.UTC_PLUS_7_TIMEZONE,
      offset: 7,
      currentUTC: now.toISOString(),
      currentUTC7: utc7Now.toISOString(),
    };
  }
}
