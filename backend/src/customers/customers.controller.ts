import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common';
import { CustomersService } from './customers.service';
import { CreateCustomerDto } from './dto/create-customer.dto';
import { UpdateCustomerDto } from './dto/update-customer.dto';
import { AssignRoomDto } from './dto/assign-room.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { User } from '../auth/decorators/user.decorator';

@Controller('customers')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles('ADMIN')
export class CustomersController {
  constructor(private readonly customersService: CustomersService) {}

  @Post()
  create(@Body() createCustomerDto: CreateCustomerDto, @User() user: any) {
    return this.customersService.create(createCustomerDto, user.id);
  }

  @Get()
  findAllByBuilding(
    @Query('buildingId') buildingId: string,
    @User() user: any,
  ) {
    return this.customersService.findAllByBuilding(buildingId, user.id);
  }

  @Get(':id')
  findOne(@Param('id') id: string, @User() user: any) {
    return this.customersService.findOne(id, user.id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateCustomerDto: UpdateCustomerDto,
    @User() user: any,
  ) {
    return this.customersService.update(id, updateCustomerDto, user.id);
  }

  @Delete(':id')
  remove(@Param('id') id: string, @User() user: any) {
    return this.customersService.remove(id, user.id);
  }

  @Get('/rooms/:roomId')
  findCustomersByRoomId(@Param('roomId') roomId: string, @User() user: any) {
    return this.customersService.findCustomersByRoomId(roomId, user.id);
  }

  @Get(':customerId/rooms/assignments')
  findRoomAssignments(
    @Param('customerId') customerId: string,
    @User() user: any,
  ) {
    return this.customersService.findRoomAssignments(customerId, user.id);
  }

  @Post('/rooms/assign')
  assignRoom(@Body() assignRoomDto: AssignRoomDto, @User() user: any) {
    return this.customersService.assignRoom(assignRoomDto, user.id);
  }

  @Delete(':customerId/rooms/:roomId')
  removeRoomAssignment(
    @Param('customerId') customerId: string,
    @Param('roomId') roomId: string,
    @User() user: any,
  ) {
    return this.customersService.removeRoomAssignment(
      customerId,
      roomId,
      user.id,
    );
  }
}
