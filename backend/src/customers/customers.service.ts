import {
  Injectable,
  NotFoundException,
  ConflictException,
  ForbiddenException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateCustomerDto } from './dto/create-customer.dto';
import { UpdateCustomerDto } from './dto/update-customer.dto';
import { AssignRoomDto } from './dto/assign-room.dto';

@Injectable()
export class CustomersService {
  constructor(private prisma: PrismaService) {}

  async create(createCustomerDto: CreateCustomerDto, userId: string) {
    // Verify user has access to the building
    await this.verifyBuildingAccess(createCustomerDto.buildingId, userId);

    // Check if customer with same ID number already exists in this building
    const existingCustomer = await this.prisma.customer.findFirst({
      where: {
        idNumber: createCustomerDto.idNumber,
        buildingId: createCustomerDto.buildingId,
      },
    });

    if (existingCustomer) {
      throw new ConflictException(
        'Customer with this ID number already exists in this building',
      );
    }

    return this.prisma.customer.create({
      data: createCustomerDto,
      include: {
        building: true,
        roomAssignments: {
          include: {
            room: true,
          },
        },
      },
    });
  }

  async findAllByBuilding(buildingId: string, userId: string) {
    // Verify user has access to the building
    await this.verifyBuildingAccess(buildingId, userId);

    return this.prisma.customer.findMany({
      where: { buildingId },
      include: {
        building: true,
      },
      orderBy: { nameEn: 'asc' },
    });
  }

  async findOne(id: string, userId: string) {
    const customer = await this.prisma.customer.findUnique({
      where: { id },
      include: {
        building: true,
        roomAssignments: {
          include: {
            room: true,
          },
        },
      },
    });

    if (!customer) {
      throw new NotFoundException('Customer not found');
    }

    // Verify user has access to the building
    await this.verifyBuildingAccess(customer.buildingId, userId);

    return customer;
  }

  async update(
    id: string,
    updateCustomerDto: UpdateCustomerDto,
    userId: string,
  ) {
    const customer = await this.findOne(id, userId);

    // Check for ID number conflicts if ID number is being changed
    if (
      updateCustomerDto.idNumber &&
      updateCustomerDto.idNumber !== customer.idNumber
    ) {
      const buildingId = updateCustomerDto.buildingId || customer.buildingId;
      const existingCustomer = await this.prisma.customer.findFirst({
        where: {
          idNumber: updateCustomerDto.idNumber,
          buildingId,
          id: { not: id },
        },
      });

      if (existingCustomer) {
        throw new ConflictException(
          'Customer with this ID number already exists in this building',
        );
      }
    }

    return this.prisma.customer.update({
      where: { id },
      data: updateCustomerDto,
      include: {
        building: true,
        roomAssignments: {
          include: {
            room: true,
          },
        },
      },
    });
  }

  async remove(id: string, userId: string) {
    const customer = await this.findOne(id, userId);

    return this.prisma.customer.delete({
      where: { id },
    });
  }

  async findCustomersByRoomId(roomId: string, userId: string) {
    const room = await this.prisma.room.findUnique({
      where: { id: roomId },
    });

    if (!room) {
      throw new NotFoundException('Room not found');
    }

    // Verify user has access to the building
    await this.verifyBuildingAccess(room.buildingId, userId);

    return this.prisma.customerRoomAssignment.findMany({
      where: { roomId },
      include: {
        customer: true,
      },
    });
  }

  async findRoomAssignments(customerId: string, userId: string) {
    const customer = await this.findOne(customerId, userId);

    return this.prisma.customerRoomAssignment.findMany({
      where: { customerId },
      include: {
        room: true,
      },
      orderBy: { assignedAt: 'desc' },
    });
  }

  async assignRoom(assignRoomDto: AssignRoomDto, userId: string) {
    const customer = await this.findOne(assignRoomDto.customerId, userId);

    // Verify the room exists and belongs to the same building
    const room = await this.prisma.room.findUnique({
      where: { id: assignRoomDto.roomId },
    });

    if (!room) {
      throw new NotFoundException('Room not found');
    }

    if (room.buildingId !== customer.buildingId) {
      throw new ConflictException(
        'Room must belong to the same building as the customer',
      );
    }

    // Check if assignment already exists
    const existingAssignment =
      await this.prisma.customerRoomAssignment.findUnique({
        where: {
          customerId_roomId: {
            customerId: assignRoomDto.customerId,
            roomId: assignRoomDto.roomId,
          },
        },
      });

    if (existingAssignment) {
      throw new ConflictException('Customer is already assigned to this room');
    }

    if (room.status !== 'OCCUPIED') {
      //update room status to occupied
      await this.prisma.room.update({
        where: { id: assignRoomDto.roomId },
        data: { status: 'OCCUPIED' },
      });
    }

    return this.prisma.customerRoomAssignment.create({
      data: {
        customerId: assignRoomDto.customerId,
        roomId: assignRoomDto.roomId,
      },
      include: {
        customer: true,
        room: true,
      },
    });
  }

  async removeRoomAssignment(
    customerId: string,
    roomId: string,
    userId: string,
  ) {
    const customer = await this.findOne(customerId, userId);

    const assignment = await this.prisma.customerRoomAssignment.findUnique({
      where: {
        customerId_roomId: {
          customerId,
          roomId,
        },
      },
    });

    if (!assignment) {
      throw new NotFoundException('Room assignment not found');
    }

    return this.prisma.customerRoomAssignment.delete({
      where: {
        customerId_roomId: {
          customerId,
          roomId,
        },
      },
    });
  }

  private async verifyBuildingAccess(buildingId: string, userId: string) {
    const building = await this.prisma.building.findFirst({
      where: {
        id: buildingId,
        OR: [
          { createdBy: userId },
          {
            userAccess: {
              some: {
                userId,
              },
            },
          },
        ],
      },
    });

    if (!building) {
      throw new ForbiddenException('Access denied to this building');
    }

    return building;
  }
}
