import { IsString, <PERSON>Optional, <PERSON>NotEmpty, IsUUID } from 'class-validator';

export class CreateCustomerDto {
  @IsString()
  @IsNotEmpty()
  nameEn: string;

  @IsString()
  @IsOptional()
  nameTh?: string;

  @IsString()
  @IsNotEmpty()
  idNumber: string;

  @IsString()
  @IsNotEmpty()
  phoneNumber: string;

  @IsString()
  @IsOptional()
  address?: string;

  @IsUUID()
  @IsNotEmpty()
  buildingId: string;
}
