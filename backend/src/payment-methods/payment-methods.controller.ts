import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { PaymentMethodsService } from './payment-methods.service';
import { CreatePaymentMethodDto } from './dto/create-payment-method.dto';
import { UpdatePaymentMethodDto } from './dto/update-payment-method.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { User } from '../auth/decorators/user.decorator';

@ApiTags('Payment Methods')
@Controller('payment-methods')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class PaymentMethodsController {
  constructor(private readonly paymentMethodsService: PaymentMethodsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new payment method (Admin only)' })
  @ApiResponse({
    status: 201,
    description: 'Payment method created successfully',
  })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin only' })
  @ApiResponse({
    status: 409,
    description: 'Payment method name already exists',
  })
  create(
    @Body() createPaymentMethodDto: CreatePaymentMethodDto,
    @User() user: any,
  ) {
    return this.paymentMethodsService.create(
      createPaymentMethodDto,
      user.id,
      user.role,
    );
  }

  @Get()
  @ApiOperation({ summary: 'Get all payment methods (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Payment methods retrieved successfully',
  })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin only' })
  findAll(@User() user: any) {
    return this.paymentMethodsService.findAll(user.id, user.role);
  }

  @Get('active')
  @ApiOperation({ summary: 'Get active payment methods for selection' })
  @ApiResponse({
    status: 200,
    description: 'Active payment methods retrieved successfully',
  })
  findActive(@User() user: any) {
    return this.paymentMethodsService.findActive(user.id, user.role);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get payment method by ID (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Payment method retrieved successfully',
  })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin only' })
  @ApiResponse({ status: 404, description: 'Payment method not found' })
  findOne(@Param('id') id: string, @User() user: any) {
    return this.paymentMethodsService.findOne(id, user.id, user.role);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update payment method (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Payment method updated successfully',
  })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin only' })
  @ApiResponse({ status: 404, description: 'Payment method not found' })
  @ApiResponse({
    status: 409,
    description: 'Payment method name already exists',
  })
  update(
    @Param('id') id: string,
    @Body() updatePaymentMethodDto: UpdatePaymentMethodDto,
    @User() user: any,
  ) {
    return this.paymentMethodsService.update(
      id,
      updatePaymentMethodDto,
      user.id,
      user.role,
    );
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete payment method (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Payment method deleted successfully',
  })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin only' })
  @ApiResponse({ status: 404, description: 'Payment method not found' })
  @ApiResponse({
    status: 409,
    description: 'Cannot delete payment method that is being used',
  })
  remove(@Param('id') id: string, @User() user: any) {
    return this.paymentMethodsService.remove(id, user.id, user.role);
  }
}
