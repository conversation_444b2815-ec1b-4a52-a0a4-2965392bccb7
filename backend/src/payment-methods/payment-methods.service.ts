import {
  Injectable,
  NotFoundException,
  ConflictException,
  ForbiddenException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreatePaymentMethodDto } from './dto/create-payment-method.dto';
import { UpdatePaymentMethodDto } from './dto/update-payment-method.dto';

@Injectable()
export class PaymentMethodsService {
  constructor(private prisma: PrismaService) {}

  async create(
    createPaymentMethodDto: CreatePaymentMethodDto,
    userId: string,
    userRole: string,
  ) {
    // Only admins can create payment methods
    if (userRole !== 'ADMIN') {
      throw new ForbiddenException('Only admins can create payment methods');
    }

    // Check if payment method name already exists for this user
    const existingMethod = await this.prisma.paymentMethod.findFirst({
      where: {
        name: createPaymentMethodDto.name,
        createdBy: userId,
      },
    });

    if (existingMethod) {
      throw new ConflictException(
        'Payment method with this name already exists',
      );
    }

    return this.prisma.paymentMethod.create({
      data: {
        ...createPaymentMethodDto,
        createdBy: userId,
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            username: true,
          },
        },
      },
    });
  }

  async findAll(userId: string, userRole: string) {
    // Only admins can view payment methods
    if (userRole !== 'ADMIN') {
      throw new ForbiddenException('Only admins can view payment methods');
    }

    // Return only payment methods created by this admin user
    return this.prisma.paymentMethod.findMany({
      where: {
        createdBy: userId,
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            username: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async findActive(userId: string, userRole: string) {
    // Only authenticated users can access payment methods for selection
    if (userRole !== 'ADMIN') {
      throw new ForbiddenException('Only admins can access payment methods');
    }

    // Return only active payment methods created by this admin user
    return this.prisma.paymentMethod.findMany({
      where: {
        isActive: true,
        createdBy: userId,
      },
      select: {
        id: true,
        name: true,
        description: true,
      },
      orderBy: {
        name: 'asc',
      },
    });
  }

  async findOne(id: string, userId: string, userRole: string) {
    if (userRole !== 'ADMIN') {
      throw new ForbiddenException(
        'Only admins can view payment method details',
      );
    }

    const paymentMethod = await this.prisma.paymentMethod.findUnique({
      where: {
        id,
        createdBy: userId, // Ensure user can only access their own payment methods
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            username: true,
          },
        },
      },
    });

    if (!paymentMethod) {
      throw new NotFoundException('Payment method not found');
    }

    return paymentMethod;
  }

  async update(
    id: string,
    updatePaymentMethodDto: UpdatePaymentMethodDto,
    userId: string,
    userRole: string,
  ) {
    if (userRole !== 'ADMIN') {
      throw new ForbiddenException('Only admins can update payment methods');
    }

    const existingMethod = await this.prisma.paymentMethod.findUnique({
      where: {
        id,
        createdBy: userId, // Ensure user can only update their own payment methods
      },
    });

    if (!existingMethod) {
      throw new NotFoundException('Payment method not found');
    }

    // Check if name is being changed and if new name already exists for this user
    if (
      updatePaymentMethodDto.name &&
      updatePaymentMethodDto.name !== existingMethod.name
    ) {
      const nameExists = await this.prisma.paymentMethod.findFirst({
        where: {
          name: updatePaymentMethodDto.name,
          createdBy: userId,
        },
      });

      if (nameExists) {
        throw new ConflictException(
          'Payment method with this name already exists',
        );
      }
    }

    return this.prisma.paymentMethod.update({
      where: { id },
      data: updatePaymentMethodDto,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            username: true,
          },
        },
      },
    });
  }

  async remove(id: string, userId: string, userRole: string) {
    if (userRole !== 'ADMIN') {
      throw new ForbiddenException('Only admins can delete payment methods');
    }

    const existingMethod = await this.prisma.paymentMethod.findUnique({
      where: {
        id,
        createdBy: userId, // Ensure user can only delete their own payment methods
      },
    });

    if (!existingMethod) {
      throw new NotFoundException('Payment method not found');
    }

    // Check if payment method is being used in any payment entries
    const paymentEntriesCount = await this.prisma.billPaymentEntriy.count({
      where: { paymentMethodName: existingMethod.name },
    });

    if (paymentEntriesCount > 0) {
      throw new ConflictException(
        'Cannot delete payment method that is being used in payment entries. Consider deactivating it instead.',
      );
    }

    return this.prisma.paymentMethod.delete({
      where: { id },
    });
  }
}
