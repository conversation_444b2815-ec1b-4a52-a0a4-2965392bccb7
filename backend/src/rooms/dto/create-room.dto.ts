import {
  IsString,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsEnum,
  Min,
  IsArray,
  ValidateNested,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { RoomStatus } from '@prisma/client';
import { Transform, Type } from 'class-transformer';

export class CreateAdditionalServiceDto {
  @ApiProperty({ example: 'Parking' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ example: 50.0 })
  @IsNumber()
  @Min(0)
  @Transform(({ value }) => parseFloat(value))
  price: number;

  @ApiProperty({ example: 'Monthly parking fee', required: false })
  @IsOptional()
  @IsString()
  description?: string;
}

export class CreateRoomDto {
  @ApiProperty({ example: 'A101' })
  @IsString()
  @IsNotEmpty()
  roomNumber: string;

  @ApiProperty({ example: 1500.0 })
  @IsNumber()
  @Min(0)
  @Transform(({ value }) => parseFloat(value))
  monthlyRent: number;

  @ApiProperty({ example: 100.5, required: false })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Transform(({ value }) => (value ? parseFloat(value) : 0))
  initialWaterReading?: number;

  @ApiProperty({ example: 250.75, required: false })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Transform(({ value }) => (value ? parseFloat(value) : 0))
  initialElectricityReading?: number;

  @ApiProperty({ example: 'AVAILABLE', enum: RoomStatus, required: false })
  @IsOptional()
  @IsEnum(RoomStatus)
  status?: RoomStatus;

  @ApiProperty({ type: [CreateAdditionalServiceDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateAdditionalServiceDto)
  additionalServices?: CreateAdditionalServiceDto[];
}
