import {
  Is<PERSON><PERSON>,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsEnum,
  Min,
  IsArray,
  ValidateNested,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { RoomStatus } from '@prisma/client';
import { CreateAdditionalServiceDto } from './create-room.dto';
import { UpdateAdditionalServiceDto } from 'src/additional-services/dto/update-additional-service.dto';
import { DeleteAdditionalServiceDto } from 'src/additional-services/dto/delete-additional-service.dto';

export class UpdateRoomDto {
  @ApiProperty({ example: 'A101' })
  @IsString()
  @IsNotEmpty()
  roomNumber: string;

  @ApiProperty({ example: 1500.0 })
  @IsNumber()
  @Min(0)
  @Transform(({ value }) => parseFloat(value))
  monthlyRent: number;

  @ApiProperty({ example: 'AVAILABLE', enum: RoomStatus, required: false })
  @IsOptional()
  @IsEnum(RoomStatus)
  status?: RoomStatus;

  @ApiProperty({ type: [CreateAdditionalServiceDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateAdditionalServiceDto)
  add?: CreateAdditionalServiceDto[];

  @ApiProperty({ type: [UpdateAdditionalServiceDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateAdditionalServiceDto)
  update?: UpdateAdditionalServiceDto[];

  @ApiProperty({ type: [DeleteAdditionalServiceDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DeleteAdditionalServiceDto)
  remove?: DeleteAdditionalServiceDto[];
}
