import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { RoomsService } from './rooms.service';
import { CreateRoomDto } from './dto/create-room.dto';
import { UpdateRoomDto } from './dto/update-room.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { BuildingAccessGuard } from 'src/auth/guards/building-access.guard';
import { RoomAccessGuard } from '../auth/guards/room-access.guard';

@ApiTags('Rooms')
@Controller('buildings/:buildingId/rooms')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class RoomsController {
  constructor(private readonly roomsService: RoomsService) {}

  @Post()
  @UseGuards(BuildingAccessGuard)
  @ApiOperation({ summary: 'Create a new room in a building' })
  @ApiResponse({ status: 201, description: 'Room successfully created' })
  @ApiResponse({ status: 409, description: 'Room number already exists' })
  create(
    @Param('buildingId') buildingId: string,
    @Body() createRoomDto: CreateRoomDto,
  ) {
    return this.roomsService.create(buildingId, createRoomDto);
  }

  @Get()
  @UseGuards(BuildingAccessGuard)
  @ApiOperation({ summary: 'Get all rooms in a building' })
  @ApiResponse({ status: 200, description: 'Rooms retrieved successfully' })
  findAll(@Param('buildingId') buildingId: string) {
    return this.roomsService.findAll(buildingId);
  }

  @Get('/number/status')
  @UseGuards(BuildingAccessGuard)
  @ApiOperation({ summary: 'Get all rooms in a building' })
  @ApiResponse({ status: 200, description: 'Rooms retrieved successfully' })
  findRoomNumbersAndStatus(@Param('buildingId') buildingId: string) {
    return this.roomsService.findRoomNumbersAndStatus(buildingId);
  }

  @Get(':id')
  @UseGuards(BuildingAccessGuard)
  @ApiOperation({ summary: 'Get a room by ID' })
  @ApiResponse({ status: 200, description: 'Room retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Room not found' })
  findOne(@Param('id') id: string) {
    return this.roomsService.findOne(id);
  }

  //change to post
  @Patch(':id')
  @UseGuards(BuildingAccessGuard)
  @ApiOperation({ summary: 'Update a room' })
  @ApiResponse({ status: 200, description: 'Room updated successfully' })
  @ApiResponse({ status: 404, description: 'Room not found' })
  update(@Param('id') id: string, @Body() updateRoomDto: UpdateRoomDto) {
    return this.roomsService.update(id, updateRoomDto);
  }

  @Delete(':id')
  @UseGuards(BuildingAccessGuard)
  @ApiOperation({ summary: 'Delete a room' })
  @ApiResponse({ status: 200, description: 'Room deleted successfully' })
  @ApiResponse({ status: 404, description: 'Room not found' })
  remove(@Param('id') id: string) {
    return this.roomsService.remove(id);
  }
}

// Additional controller for room operations that don't require building ID in path
@ApiTags('Rooms')
@Controller('rooms')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class RoomsDirectController {
  constructor(private readonly roomsService: RoomsService) {}

  @Get(':id')
  @UseGuards(RoomAccessGuard)
  @ApiOperation({ summary: 'Get a room by ID (direct access)' })
  @ApiResponse({ status: 200, description: 'Room retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Room not found' })
  findOne(@Param('id') id: string) {
    return this.roomsService.findOne(id);
  }

  @Get('/services/:id')
  @UseGuards(RoomAccessGuard)
  @ApiOperation({ summary: 'Get a room by ID (direct access)' })
  @ApiResponse({ status: 200, description: 'Room retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Room not found' })
  findAdditionalServices(@Param('id') id: string) {
    return this.roomsService.findAdditonalServices(id);
  }

  @Patch(':id')
  @UseGuards(RoomAccessGuard)
  @ApiOperation({ summary: 'Update a room (direct access)' })
  @ApiResponse({ status: 200, description: 'Room updated successfully' })
  @ApiResponse({ status: 404, description: 'Room not found' })
  update(@Param('id') id: string, @Body() updateRoomDto: UpdateRoomDto) {
    return this.roomsService.update(id, updateRoomDto);
  }

  @Delete(':id')
  @UseGuards(RoomAccessGuard)
  @ApiOperation({ summary: 'Delete a room (direct access)' })
  @ApiResponse({ status: 200, description: 'Room deleted successfully' })
  @ApiResponse({ status: 404, description: 'Room not found' })
  remove(@Param('id') id: string) {
    return this.roomsService.remove(id);
  }
}
