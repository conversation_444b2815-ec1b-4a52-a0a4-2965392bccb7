import { Module } from '@nestjs/common';
import { RoomsService } from './rooms.service';
import { RoomsController, RoomsDirectController } from './rooms.controller';
import { PrismaModule } from '../prisma/prisma.module';
import { BuildingsModule } from 'src/buildings/buildings.module';

@Module({
  imports: [PrismaModule, BuildingsModule],
  controllers: [RoomsController, RoomsDirectController],
  providers: [RoomsService],
})
export class RoomsModule {}
