import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateRoomDto } from './dto/create-room.dto';
import { UpdateRoomDto } from './dto/update-room.dto';
import { BuildingsService } from 'src/buildings/buildings.service';
import { Prisma } from '@prisma/client';

@Injectable()
export class RoomsService {
  constructor(
    private prisma: PrismaService,
    private buildingsService: BuildingsService,
  ) {}

  async create(buildingId: string, createRoomDto: CreateRoomDto) {
    // Check if building exists and user has access
    const building = await this.prisma.building.findUnique({
      where: { id: buildingId },
    });

    if (!building) {
      throw new NotFoundException('Building not found');
    }

    // Check if room number already exists in this building
    const existingRoom = await this.prisma.room.findUnique({
      where: {
        buildingId_roomNumber: {
          buildingId,
          roomNumber: createRoomDto.roomNumber,
        },
      },
    });

    if (existingRoom) {
      throw new ConflictException(
        'Room number already exists in this building',
      );
    }

    // Prepare additional services data
    const additionalServicesData = createRoomDto.additionalServices?.length
      ? (createRoomDto.additionalServices as any[]).map((service: any) => ({
          name: String(service.name),
          price: Number(service.price),
          description: service.description
            ? String(service.description)
            : undefined,
        }))
      : undefined;

    return this.prisma.room.create({
      data: {
        roomNumber: createRoomDto.roomNumber,
        monthlyRent: createRoomDto.monthlyRent,
        status: createRoomDto.status,
        buildingId,
        currentWaterReading: createRoomDto.initialWaterReading || 0,
        currentElectricityReading: createRoomDto.initialElectricityReading || 0,
        ...(additionalServicesData && {
          additionalServices: {
            create: additionalServicesData,
          },
        }),
      },
      include: {
        building: {
          select: {
            id: true,
            name: true,
          },
        },
        additionalServices: true,
        _count: {
          select: {
            billingEntries: true,
          },
        },
      },
    });
  }

  async findRoomNumbersAndStatus(buildingId: string) {
    // Check if building exists and user has access
    const building = await this.prisma.building.findUnique({
      where: { id: buildingId },
    });

    if (!building) {
      throw new NotFoundException('Building not found');
    }

    const rooms = await this.prisma.$queryRaw<
      Array<{
        id: string;
        roomNumber: string;
        status: 'AVAILABLE' | 'OCCUPIED' | 'MAINTENANCE';
      }>
    >(
      Prisma.sql`
        SELECT
          id,
          room_number AS "roomNumber",
          status AS "status"
        FROM rooms
        WHERE building_id = ${buildingId}
        ORDER BY room_number ASC;
      `,
    );

    return rooms;
  }

  async findAll(buildingId: string) {
    // Check if building exists and user has access
    const building = await this.prisma.building.findUnique({
      where: { id: buildingId },
    });

    if (!building) {
      throw new NotFoundException('Building not found');
    }

    const rooms = await this.prisma.$queryRaw<
      Array<{
        id: string;
        buildingId: string;
        roomNumber: string;
        monthlyRent: number;
        initialWaterReading: number;
        initialElectricityReading: number;
        currentWaterReading: number;
        currentElectricityReading: number;
        status: 'AVAILABLE' | 'OCCUPIED' | 'MAINTENANCE';
        paymentStatus: string;
        currentBillingDate: Date;
      }>
    >(
      Prisma.sql`
        SELECT
          tmp.id,
          tmp.building_id AS "buildingId",
          tmp.room_number AS "roomNumber",
          tmp.monthly_rent AS "monthlyRent",
          tmp.initial_water_reading AS "initialWaterReading",
          tmp.initial_electricity_reading AS "initialElectricityReading",
          tmp.current_water_reading AS "currentWaterReading",
          tmp.current_electricity_reading AS "currentElectricityReading",
          tmp.room_status AS "status",
          coalesce(tmp.payment_status, 'UNPAID') AS "paymentStatus",
          tmp.created_at as "currentBillingDate"
        FROM
        (
         SELECT
            r.id,
            r.building_id,
            r.room_number,
            r.monthly_rent,
            r.initial_water_reading,
            r.initial_electricity_reading,
            r.current_water_reading,
            r.current_electricity_reading,
            r.status AS room_status,
            be.status AS payment_status,
            be.created_at as created_at,
            ROW_NUMBER() OVER (PARTITION BY r.id ORDER BY be.created_at DESC) AS rn
            FROM rooms r
            LEFT JOIN billing_entries be ON r.id = be.room_id
            WHERE r.building_id = ${buildingId}
        ) AS tmp
        WHERE tmp.rn = 1
        ORDER BY tmp.room_number ASC;
      `,
    );

    return rooms;
  }

  async findOne(id: string) {
    const room = await this.prisma.room.findUnique({
      where: { id },
      include: {
        building: {
          select: {
            id: true,
            name: true,
            createdBy: true,
          },
        },
        additionalServices: true,
        billingEntries: {
          orderBy: {
            billingDate: 'desc',
          },
          take: 10,
        },
      },
    });

    if (!room) {
      throw new NotFoundException('Room not found');
    }

    return room;
  }

  async findAdditonalServices(id: string) {
    const room = await this.prisma.room.findUnique({
      where: { id },
      include: {
        additionalServices: true,
      },
    });

    if (!room) {
      throw new NotFoundException('Room not found');
    }

    return room.additionalServices;
  }

  async update(id: string, updateRoomDto: UpdateRoomDto) {
    const room = await this.findOne(id);

    // If room number is being updated, check for conflicts
    if (
      updateRoomDto.roomNumber &&
      updateRoomDto.roomNumber !== room.roomNumber
    ) {
      throw new Error('Room number cannot be changed');
    }

    // Extract additional services from the DTO for update
    const { add, update, remove, ...roomData } = updateRoomDto;

    if (add?.length) {
      // Add new services
      await this.prisma.additionalService.createMany({
        data: add.map((service) => ({
          roomId: id,
          name: service.name,
          price: service.price,
          description: service.description,
        })),
      });
    }

    if (update?.length) {
      // Update existing services
      for (const service of update) {
        await this.prisma.additionalService.update({
          where: { id: service.id },
          data: {
            name: service.name,
            price: service.price,
            description: service.description,
          },
        });
      }
    }

    if (remove?.length) {
      // Remove services
      await this.prisma.additionalService.deleteMany({
        where: {
          id: {
            in: remove.map((service) => service.id),
          },
        },
      });
    }

    return this.prisma.room.update({
      where: { id },
      data: roomData,
    });
  }

  async remove(id: string) {
    // Verify user has access to this room before deletion
    await this.findOne(id);

    await this.prisma.room.delete({
      where: { id },
    });

    return { message: 'Room deleted successfully' };
  }
}
