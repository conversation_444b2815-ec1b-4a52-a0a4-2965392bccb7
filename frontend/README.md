# Apartment Management System - Frontend

A modern, responsive web application for multi-tenant apartment management built with Next.js 15, featuring role-based access control, utility billing, and comprehensive building management capabilities.

## 🏗️ Technology Stack

- **Framework**: Next.js 15 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Custom components with Lucide React icons
- **State Management**: React Context API
- **HTTP Client**: Axios
- **Authentication**: JWT with custom auth provider
- **PDF Generation**: jsPDF, JSZip, file-saver
- **Build Tool**: Next.js built-in bundler

## 📋 Prerequisites

Before setting up the frontend, ensure you have the following installed:

### Required Software

- **Node.js**: Version 18.0.0 or higher
- **npm**: Version 8.0.0 or higher (comes with Node.js)
- **Git**: For version control
- **Backend API**: The NestJS backend must be running (see backend README)

### Development Tools (Recommended)

- **VS Code**: With TypeScript, Tailwind CSS, and ES7+ React snippets extensions
- **Chrome DevTools**: For debugging and performance analysis
- **React Developer Tools**: Browser extension for React debugging

### System Requirements

- **RAM**: Minimum 4GB (8GB recommended)
- **Storage**: At least 500MB free space
- **OS**: Windows 10+, macOS 10.15+, or Linux
- **Browser**: Modern browser supporting ES2020+ (Chrome 88+, Firefox 85+, Safari 14+)

## 🚀 Step-by-Step Setup Instructions

### 1. Clone and Navigate to Frontend

```bash
# Clone the repository (if not already done)
git clone <repository-url>
cd apartment-management-system/frontend
```

### 2. Install Dependencies

```bash
# Install all required packages
npm install

# This will install:
# - Next.js 15 framework
# - TypeScript and type definitions
# - Tailwind CSS for styling
# - Axios for API communication
# - Lucide React for icons
# - Development tools and linting
```

### 3. Environment Configuration

Create a `.env.local` file in the frontend root directory:

```bash
# Copy the example environment file
cp .env.example .env.local
```

Edit the `.env.local` file with your configuration:

```env
# API Configuration
BACKEND_API_URL="http://localhost:3001"

# Application Configuration
NEXT_PUBLIC_APP_NAME="Apartment Management System"
NEXT_PUBLIC_APP_VERSION="1.0.0"

# Development Configuration
NODE_ENV="development"
```

**Important Notes:**

- Use `.env.local` for local development (automatically ignored by Git)
- All environment variables used in client-side code must be prefixed with `NEXT_PUBLIC_`
- The backend API must be running on the specified URL

### 4. Start the Development Server

```bash
# Start in development mode with hot reload
npm run dev

# Alternative: Start on a specific port
npm run dev -- --port 3000
```

**Expected Output:**

```
▲ Next.js 15.3.4
- Local:        http://localhost:3000
- Network:      http://************:3000

✓ Starting...
✓ Ready in 2.1s
```

### 5. Verify Setup

1. **Application Load**: Visit `http://localhost:3000` - should show the login page

2. **API Connection**: Check browser console for any API connection errors

3. **Responsive Design**: Test on different screen sizes using browser dev tools

4. **Authentication Flow**: Try registering a new account to verify backend connectivity

## 📁 Project Structure Overview

```
frontend/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── auth/              # Authentication pages
│   │   ├── admin/             # Admin dashboard pages
│   │   ├── user/              # User dashboard pages
│   │   ├── buildings/         # Building management pages
│   │   ├── globals.css        # Global styles
│   │   ├── layout.tsx         # Root layout component
│   │   └── page.tsx           # Home page
│   ├── components/            # Reusable UI components
│   │   ├── ui/               # Base UI components
│   │   ├── auth/             # Authentication components
│   │   ├── buildings/        # Building-related components
│   │   ├── rooms/            # Room management components
│   │   ├── billing/          # Billing system components
│   │   └── layout/           # Layout components
│   ├── lib/                  # Utility libraries
│   │   ├── api.ts           # API client configuration
│   │   ├── auth.tsx         # Authentication context
│   │   ├── utils.ts         # Utility functions
│   │   └── services/        # API service functions
│   ├── types/               # TypeScript type definitions
│   │   └── index.ts         # Shared types
│   └── hooks/               # Custom React hooks
├── public/                  # Static assets
├── .env.local              # Environment variables
├── tailwind.config.js      # Tailwind CSS configuration
├── next.config.js          # Next.js configuration
├── package.json           # Dependencies and scripts
└── README.md              # This file
```

### Key Architecture Concepts

**App Router**: Uses Next.js 15's new App Router for file-based routing and layouts.

**Component Architecture**: Modular components organized by feature with reusable UI components.

**Type Safety**: Full TypeScript integration with strict type checking.

**Responsive Design**: Mobile-first approach using Tailwind CSS utilities.

**Authentication Context**: Centralized auth state management with React Context.

## ⚡ Quick Start

For experienced developers who want to get started immediately:

```bash
# 1. Clone and navigate
git clone <repository-url>
cd apartment-management-system/frontend

# 2. Install dependencies
npm install

# 3. Setup environment
cp .env.example .env.local
# Edit .env.local with your API URL

# 4. Start development server
npm run dev
```

**Verify setup**: Visit `http://localhost:3000` to see the application.

**Note**: Ensure the backend API is running on `http://localhost:3001` before starting the frontend.

## 🏗️ Creating Next.js Project from Scratch

If you want to understand how this frontend was created, here are the commands used:

### 1. Initial Next.js Setup

```bash
# Create new Next.js project with TypeScript and Tailwind CSS
npx create-next-app@latest apartment-management-frontend --typescript --tailwind --eslint --app --src-dir --import-alias "@/*"

cd apartment-management-frontend

# Install additional dependencies
npm install axios
npm install lucide-react
npm install @types/node

# Install development dependencies
npm install -D @types/react @types/react-dom
npm install -D eslint-config-prettier prettier
npm install -D @typescript-eslint/eslint-plugin @typescript-eslint/parser
```

### 2. Setup Project Structure

```bash
# Create directory structure
mkdir -p src/components/ui
mkdir -p src/components/auth
mkdir -p src/components/buildings
mkdir -p src/components/rooms
mkdir -p src/components/billing
mkdir -p src/components/layout
mkdir -p src/lib/services
mkdir -p src/hooks
mkdir -p src/types

# Create initial files
touch src/lib/api.ts
touch src/lib/auth.tsx
touch src/lib/utils.ts
touch src/types/index.ts
```

### 3. Setup App Router Pages

```bash
# Create page directories (App Router structure)
mkdir -p src/app/auth
mkdir -p src/app/admin
mkdir -p src/app/user
mkdir -p src/app/buildings

# Create page files
touch src/app/auth/page.tsx
touch src/app/admin/page.tsx
touch src/app/user/page.tsx
touch src/app/buildings/page.tsx
touch src/app/buildings/[id]/page.tsx
```

### 4. Configure Tailwind CSS

```bash
# Tailwind config is automatically created
# Additional configuration added to tailwind.config.js for custom styles
```

### 5. Setup TypeScript Configuration

```bash
# TypeScript config is automatically created
# Additional path mapping configured in tsconfig.json for @/* imports
```

### 6. Create Custom Components

```bash
# UI Components
touch src/components/ui/Button.tsx
touch src/components/ui/Card.tsx
touch src/components/ui/Modal.tsx
touch src/components/ui/Input.tsx

# Feature Components
touch src/components/auth/LoginForm.tsx
touch src/components/auth/RegisterForm.tsx
touch src/components/buildings/BuildingCard.tsx
touch src/components/rooms/RoomCard.tsx
touch src/components/billing/BillingForm.tsx
```

### 7. Setup API Integration

```bash
# API client configuration in src/lib/api.ts
# Service functions in src/lib/services/
touch src/lib/services/auth.ts
touch src/lib/services/buildings.ts
touch src/lib/services/rooms.ts
touch src/lib/services/billing.ts
```

### 8. Configure Environment Variables

```bash
# Create environment files
touch .env.local
touch .env.example

# Add environment variables for API URL and other configurations
```

## 📋 Essential Commands

### Development Commands

```bash
# Start development server with hot reload
npm run dev

# Start development server on specific port
npm run dev -- --port 3002

# Start with Turbopack (faster builds)
npm run dev -- --turbo

# Build for production
npm run build

# Start production server
npm run start

# Export static files (if applicable)
npm run export
```

### Code Quality Commands

```bash
# Run TypeScript type checking
npm run type-check

# Run ESLint
npm run lint

# Fix ESLint issues automatically
npm run lint --fix

# Format code with Prettier
npm run format

# Check code formatting
npm run format:check
```

### Testing Commands

```bash
# Run unit tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run E2E tests (if configured)
npm run test:e2e
```

### Build Analysis Commands

```bash
# Analyze bundle size
npm run analyze

# Build and analyze
npm run build:analyze

# Check for outdated dependencies
npm outdated

# Update dependencies
npm update
```

## 🔧 Common Issues & Troubleshooting

### Development Server Issues

**Problem**: `Error: listen EADDRINUSE: address already in use :::3000`

```bash
# Solution 1: Kill process using port 3000
lsof -ti:3000 | xargs kill -9

# Solution 2: Use a different port
npm run dev -- --port 3002
```

**Problem**: `Module not found` errors

```bash
# Solution: Clear Next.js cache and reinstall
rm -rf .next node_modules package-lock.json
npm install
npm run dev
```

### API Connection Issues

**Problem**: CORS errors in browser console

- **Solution**: Ensure backend CORS is configured for frontend URL
- **Check**: Verify `BACKEND_API_URL` in `.env.local`
- **Backend**: Confirm backend is running on specified port

**Problem**: Authentication not working

- **Solution**: Check if JWT tokens are being stored in localStorage
- **Debug**: Open browser dev tools → Application → Local Storage
- **Verify**: Ensure backend JWT_SECRET is configured

### Build Issues

**Problem**: TypeScript compilation errors

```bash
# Solution: Check for type errors
npm run type-check

# Fix common issues
npm run lint --fix
```

**Problem**: Tailwind styles not applying

```bash
# Solution: Rebuild Tailwind
npm run build:css

# Check Tailwind config
npx tailwindcss --help
```

### Environment Variables

**Problem**: Environment variables not loading

- **Solution**: Ensure variables are prefixed with `NEXT_PUBLIC_`
- **Check**: Restart development server after changing `.env.local`
- **Verify**: Use `console.log(process.env.BACKEND_API_URL)` to debug

## 🧪 Testing Instructions

### 1. Manual Testing Workflow

#### Authentication Testing

```bash
# 1. Open application at http://localhost:3000
# 2. Click "Sign Up" to create new account
# 3. Fill form with:
#    - Email: <EMAIL>
#    - Username: admin
#    - Password: password123
# 4. Verify redirect to dashboard
# 5. Test logout functionality
# 6. Test login with created credentials
```

#### Building Management Testing

```bash
# 1. Login as ADMIN user
# 2. Navigate to "Buildings" section
# 3. Click "Create Building"
# 4. Enter building name: "Test Building"
# 5. Verify building appears in list
# 6. Click on building to view details
# 7. Test room creation within building
```

#### User Role Testing

```bash
# 1. As ADMIN, create a USER account for a building
# 2. Logout and login as USER
# 3. Verify USER only sees assigned building
# 4. Test billing entry creation
# 5. Verify access restrictions work correctly
```

### 2. Automated Testing

```bash
# Run type checking
npm run type-check

# Run linting
npm run lint

# Run build test
npm run build

# Test production build locally
npm run start
```

### 3. Performance Testing

```bash
# Analyze bundle size
npm run analyze

# Test Lighthouse scores
# Open Chrome DevTools → Lighthouse → Run audit
```

## 🎯 Key Features to Test

### Responsive Design

1. **Mobile View**: Test on screen widths 320px-768px
2. **Tablet View**: Test on screen widths 768px-1024px
3. **Desktop View**: Test on screen widths 1024px+
4. **Navigation**: Verify mobile menu works correctly

### User Experience

1. **Loading States**: Check loading indicators during API calls
2. **Error Handling**: Test with network disconnected
3. **Form Validation**: Test all form inputs with invalid data
4. **Accessibility**: Test keyboard navigation and screen reader compatibility

### Multi-Tenant Features

1. **Role-Based UI**: Verify different interfaces for ADMIN vs USER
2. **Data Isolation**: Confirm users only see their assigned buildings
3. **Permission Checks**: Test unauthorized access attempts

### Billing System

1. **Pricing Configuration**: Test building-specific pricing setup
2. **Bill Creation**: Test utility bill entry and calculation
3. **History View**: Test billing history display and filtering
4. **Export Features**: Test data export functionality
5. **PDF Generation**: Test individual and bulk PDF download functionality

### PDF Generation Features

The application includes comprehensive PDF generation capabilities for billing documents:

#### Individual PDF Generation

- **Single Bill PDF**: Generate and download PDF for individual billing entries
- **Real-time Generation**: PDFs are generated on-demand with current data
- **Professional Format**: Includes building info, customer details, and itemized charges
- **File Naming**: Auto-generated filenames with room number and date

#### Bulk PDF Download

- **ZIP Archive**: Download all billing entries as a ZIP file containing individual PDFs
- **Progress Tracking**: Real-time progress indicator during bulk generation
- **Error Handling**: Graceful handling of individual PDF generation failures
- **Batch Processing**: Efficient processing of large numbers of billing entries

#### Technical Implementation

- **Libraries Used**: jsPDF for PDF generation, JSZip for archive creation, file-saver for downloads
- **PDF Structure**: Professional invoice layout with header, customer info, itemized table, and totals
- **Performance**: Optimized for handling large datasets with progress feedback
- **Error Recovery**: Individual PDF failures don't stop the entire bulk process

#### Usage Instructions

1. **Individual PDF**: Click the "Print" button next to any billing entry
2. **Bulk Download**: Use the "Download All PDFs" button in the filter section
3. **Progress Monitoring**: Watch the progress indicator during bulk operations
4. **File Management**: PDFs are automatically named and organized in ZIP archives

## 📚 Additional Resources

### Documentation

- [Next.js Documentation](https://nextjs.org/docs)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [React Documentation](https://react.dev/)

### Development Tools

- **Next.js DevTools**: Built-in development features
- **TypeScript Compiler**: `npx tsc --noEmit` for type checking
- **Tailwind Play**: [play.tailwindcss.com](https://play.tailwindcss.com) for testing styles

### Useful Commands

```bash
# Development
npm run dev              # Start development server
npm run build           # Build for production
npm run start           # Start production server
npm run lint            # Run ESLint
npm run type-check      # Run TypeScript compiler

# Debugging
npm run dev -- --turbo  # Start with Turbopack (faster)
npm run build -- --debug # Build with debug info
```

### Next Steps for Development

1. **Add Unit Tests**: Implement Jest and React Testing Library
2. **E2E Testing**: Add Playwright or Cypress for end-to-end tests
3. **Performance Optimization**: Implement code splitting and lazy loading
4. **PWA Features**: Add service worker for offline functionality
5. **Internationalization**: Add multi-language support with next-i18next
6. **Analytics**: Integrate analytics tracking for user behavior

## 🚀 Production Deployment

### Build for Production

```bash
# Create optimized production build
npm run build

# Test production build locally
npm run start
```

### Environment Setup

Create production environment file:

```env
# .env.production
BACKEND_API_URL="https://your-api-domain.com"
NEXT_PUBLIC_APP_NAME="Apartment Management System"
NEXT_PUBLIC_APP_VERSION="1.0.0"
NODE_ENV="production"
```

### Deployment Options

#### Vercel (Recommended)

```bash
# Install Vercel CLI
npm i -g vercel

# Deploy to Vercel
vercel --prod
```

#### Docker Deployment

```dockerfile
# Dockerfile
FROM node:18-alpine AS deps
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine AS builder
WORKDIR /app
COPY . .
COPY --from=deps /app/node_modules ./node_modules
RUN npm run build

FROM node:18-alpine AS runner
WORKDIR /app
ENV NODE_ENV production
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static
EXPOSE 3000
CMD ["node", "server.js"]
```

#### Static Export (if applicable)

```bash
# For static hosting
npm run build
npm run export
```

### Performance Optimization

#### Bundle Analysis

```bash
# Analyze bundle size
npm run build
npx @next/bundle-analyzer
```

#### Image Optimization

- Use Next.js `Image` component for automatic optimization
- Implement proper image formats (WebP, AVIF)
- Add loading="lazy" for below-the-fold images

#### Caching Strategy

- Configure proper cache headers
- Implement service worker for offline functionality
- Use Next.js built-in caching features

## 🔒 Security Considerations

### Client-Side Security

1. **Environment Variables**: Never expose sensitive data in `NEXT_PUBLIC_` variables
2. **XSS Prevention**: Sanitize user inputs and use React's built-in protections
3. **CSRF Protection**: Implement proper CSRF tokens for state-changing operations
4. **Content Security Policy**: Configure CSP headers for production

### Authentication Security

1. **Token Storage**: Store JWT tokens securely (httpOnly cookies preferred)
2. **Token Expiration**: Implement proper token refresh mechanisms
3. **Route Protection**: Ensure all protected routes check authentication
4. **Role Validation**: Verify user roles on both client and server

## 📊 Monitoring & Analytics

### Performance Monitoring

```bash
# Core Web Vitals tracking
# Add to _app.tsx or layout.tsx
export function reportWebVitals(metric) {
  console.log(metric)
  // Send to analytics service
}
```

### Error Tracking

- Implement error boundaries for graceful error handling
- Add error reporting service (Sentry, LogRocket, etc.)
- Monitor API error rates and response times

### User Analytics

- Track user interactions and feature usage
- Monitor conversion funnels (registration, building creation, etc.)
- Analyze performance metrics and user experience

## 🧪 Advanced Testing

### Unit Testing Setup

```bash
# Install testing dependencies
npm install --save-dev jest @testing-library/react @testing-library/jest-dom

# Create jest.config.js
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
}
```

### E2E Testing with Playwright

```bash
# Install Playwright
npm install --save-dev @playwright/test

# Run E2E tests
npx playwright test
```

### Component Testing

```typescript
// Example component test
import { render, screen } from "@testing-library/react";
import { AuthProvider } from "@/lib/auth";
import LoginForm from "@/components/auth/LoginForm";

test("renders login form", () => {
  render(
    <AuthProvider>
      <LoginForm />
    </AuthProvider>
  );
  expect(screen.getByLabelText(/username/i)).toBeInTheDocument();
});
```

## 📞 Support & Troubleshooting

### Common Solutions

1. **Clear Browser Cache**: Hard refresh (Ctrl+Shift+R) or clear browser data
2. **Check Network Tab**: Verify API calls are successful in browser dev tools
3. **Console Errors**: Look for JavaScript errors in browser console
4. **Environment Variables**: Ensure all required variables are set correctly
5. **Backend Connection**: Verify backend API is running and accessible

### Getting Help

1. **Check Documentation**: Review Next.js and component library docs
2. **Search Issues**: Look for similar problems in project issues
3. **Debug Mode**: Use React DevTools and Next.js debug mode
4. **Network Analysis**: Use browser dev tools to analyze API calls
5. **Performance Profiling**: Use React Profiler for performance issues

### Development Best Practices

1. **Code Organization**: Keep components small and focused
2. **Type Safety**: Use TypeScript strictly, avoid `any` types
3. **Performance**: Implement proper memoization and lazy loading
4. **Accessibility**: Follow WCAG guidelines for inclusive design
5. **Testing**: Write tests for critical user flows and components

---

## 🎉 Conclusion

This frontend application provides a modern, scalable foundation for apartment management. The setup guide covers everything from basic installation to advanced deployment strategies.

For additional support or contributions, please refer to the project documentation and follow the established development practices outlined in this guide.

**Happy coding! 🚀**
