"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import RoomsList from "@/components/rooms/RoomsList";
import RoomDetailsModal from "@/components/rooms/RoomDetailsModal";
import { useAuth } from "@/lib/auth";
import { buildingService } from "@/lib/services/buildings";
import { BillingCycle, Building, BuildingPricing, IRoomCard } from "@/types";
import Button from "@/components/ui/Button";
import { ArrowLeft } from "lucide-react";
import { billingService } from "@/lib/services/billing";

const BuildingDetailPage: React.FC = () => {
  const { user, signOut, isAdmin } = useAuth();
  const router = useRouter();
  const params = useParams();
  const buildingId = params.id as string;

  const [building, setBuilding] = useState<Building | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedRoom, setSelectedRoom] = useState<IRoomCard | null>(null);
  const [showRoomDetails, setShowRoomDetails] = useState(false);
  const [currentBillingCycle, setCurrentBillingCycle] = useState<BillingCycle>({
    id: "",
    buildingId: "",
    startDate: "",
    endDate: "",
    updatedAt: "",
    createdBy: "",
    updatedBy: "",
    status: "",
  });

  useEffect(() => {
    if (buildingId) {
      fetchBuilding();
      fetchCurrentBillingCycle();
    }
  }, [buildingId]);

  const fetchBuilding = async () => {
    setLoading(true);
    const { data, error } = await buildingService.getBuildingById(buildingId);

    if (error) {
      setError(error);
    } else if (data) {
      setBuilding(data);
    }

    setLoading(false);
  };

  const fetchCurrentBillingCycle = async () => {
    if (!buildingId) return;

    setLoading(true);
    const { data, error } = await billingService.getCurrentBillingCycle(
      buildingId
    );

    if (error) {
      setError(error);
    } else if (data) {
      setCurrentBillingCycle(data);
    }

    setLoading(false);
  };

  const handleSignOut = async () => {
    await signOut();
  };

  const handleRoomSelect = (room: IRoomCard) => {
    setSelectedRoom(room);
    setShowRoomDetails(true);
  };

  const handleCloseRoomDetails = () => {
    setShowRoomDetails(false);
    setSelectedRoom(null);
  };

  const handleRouteToPreviousPage = () => {
    if (isAdmin) {
      router.push("/buildings");
    } else {
      router.push("/dashboard");
    }
  };

  if (loading) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </ProtectedRoute>
    );
  }

  if (error || !building) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              {error || "Building not found"}
            </h1>
            <Button onClick={() => router.push("/buildings")}>
              Back to Buildings
            </Button>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center space-x-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleRouteToPreviousPage}
                  className="flex items-center space-x-2"
                >
                  <ArrowLeft size={16} />
                  <span>{isAdmin ? "Buildings" : "Dashboard"}</span>
                </Button>
                <div>
                  <h1 className="text-xl font-semibold text-gray-900">
                    {building.name}
                  </h1>
                  <p className="text-sm text-gray-500">Building Management</p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-600">
                  Welcome, {user?.email}
                </span>
                {isAdmin && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    Admin
                  </span>
                )}
                <Button onClick={handleSignOut} variant="outline" size="sm">
                  Sign Out
                </Button>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            <RoomsList
              buildingId={building.id}
              buildingName={building.name}
              onRoomSelect={handleRoomSelect}
              currentBillingCycle={currentBillingCycle}
            />
          </div>
        </main>

        {/* Room Details Modal */}
        <RoomDetailsModal
          room={selectedRoom}
          isOpen={showRoomDetails}
          onClose={handleCloseRoomDetails}
          currentBillingCycle={currentBillingCycle}
        />
      </div>
    </ProtectedRoute>
  );
};

export default BuildingDetailPage;
