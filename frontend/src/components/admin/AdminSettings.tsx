import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useAuth } from "@/lib/auth";
import { billingService } from "@/lib/services/billing";
import { buildingService } from "@/lib/services/buildings";
import { BuildingPricing, Building } from "@/types";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";
import {
  Settings,
  Droplets,
  Zap,
  ToggleLeft,
  ToggleRight,
  Save,
  Users,
  DollarSign,
  CreditCard,
} from "lucide-react";
import UserManagement from "./UserManagement";
import PaymentMethodsManagement from "./PaymentMethodsManagement";
import { formatDateThai } from "@/lib/utils";

const buildingPricingSchema = z.object({
  waterPricePerUnit: z.number().min(0, "Water price must be positive"),
  electricityPricePerUnit: z
    .number()
    .min(0, "Electricity price must be positive"),
  billingEnabled: z.boolean(),
});

type BuildingPricingFormData = z.infer<typeof buildingPricingSchema>;

const AdminSettings: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<
    "pricing" | "users" | "payment-methods"
  >("pricing");
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [buildings, setBuildings] = useState<Building[]>([]);
  const [selectedBuildingId, setSelectedBuildingId] = useState<string | null>(
    null
  );
  const [currentPricing, setCurrentPricing] = useState<BuildingPricing | null>(
    null
  );
  const [validationStatus, setValidationStatus] = useState<{
    canToggleOff: boolean;
    billingEnabledDate: string | null;
    roomsWithoutBilling: string[];
    roomsWithOldBilling: string[];
    message: string;
  } | null>(null);
  const [showValidationDetails, setShowValidationDetails] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue,
  } = useForm<BuildingPricingFormData>({
    resolver: zodResolver(buildingPricingSchema),
    defaultValues: {
      waterPricePerUnit: 0,
      electricityPricePerUnit: 0,
      billingEnabled: false,
    },
  });

  const billingEnabled = watch("billingEnabled");

  useEffect(() => {
    fetchBuildings();
  }, []);

  useEffect(() => {
    if (selectedBuildingId) {
      fetchCurrentPricing();
    }
  }, [selectedBuildingId]);

  const fetchBuildings = async () => {
    setLoading(true);
    const { data, error } = await buildingService.getBuildings();

    if (error) {
      setError(error);
    } else if (data && data.length > 0) {
      setBuildings(data);
      // Auto-select first building if user is not admin
      if (user?.role !== "ADMIN" && data.length === 1) {
        setSelectedBuildingId(data[0].id);
      }
    }

    setLoading(false);
  };

  const fetchCurrentPricing = async () => {
    if (!selectedBuildingId) return;

    setLoading(true);
    const { data, error } = await billingService.getBuildingPricing(
      selectedBuildingId
    );

    if (error) {
      setError(error);
    } else if (data) {
      setCurrentPricing(data);
      reset({
        waterPricePerUnit: Number(data.waterPricePerUnit),
        electricityPricePerUnit: Number(data.electricityPricePerUnit),
        billingEnabled: data.billingEnabled,
      });

      // Fetch validation status if billing is enabled
      if (data.billingEnabled) {
        await fetchValidationStatus();
      }
    }

    setLoading(false);
  };

  const fetchValidationStatus = async () => {
    if (!selectedBuildingId) return;

    const { data, error } = await billingService.getBillingToggleValidation(
      selectedBuildingId
    );

    if (!error && data) {
      setValidationStatus(data);
    }
  };

  const onSubmit = async (data: BuildingPricingFormData) => {
    if (!user || !selectedBuildingId) return;

    setSaving(true);
    setError(null);
    setSuccess(null);

    const isBillingEntryToggled =
      currentPricing?.billingEnabled !== data.billingEnabled;
    const billingEnabledDate = validationStatus?.billingEnabledDate
      ? validationStatus.billingEnabledDate
      : new Date().toString();

    const { data: updatedPricing, error: updateError } =
      await billingService.updateBuildingPricing(
        selectedBuildingId,
        data.waterPricePerUnit,
        data.electricityPricePerUnit,
        data.billingEnabled,
        isBillingEntryToggled,
        billingEnabledDate
      );

    if (updateError) {
      setError(updateError);
    } else if (updatedPricing) {
      await fetchCurrentPricing();
      setSuccess("Building pricing updated successfully!");
      setTimeout(() => setSuccess(null), 3000);
    }

    setSaving(false);
  };

  const toggleBilling = async () => {
    // If trying to turn OFF billing, check validation first
    if (billingEnabled && validationStatus && !validationStatus.canToggleOff) {
      setShowValidationDetails(true);
      setError(validationStatus.message);
      return;
    }

    setValue("billingEnabled", !billingEnabled);

    // If turning ON billing, clear validation status
    if (!billingEnabled) {
      setValidationStatus(null);
      setShowValidationDetails(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 flex items-center space-x-2">
          <Settings className="h-6 w-6" />
          <span>Admin Settings</span>
        </h2>
        <p className="text-gray-600">
          Manage building settings, pricing, and user access
        </p>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab("pricing")}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === "pricing"
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            }`}
          >
            <div className="flex items-center space-x-2">
              <DollarSign className="h-4 w-4" />
              <span>Pricing Settings</span>
            </div>
          </button>
          <button
            onClick={() => setActiveTab("users")}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === "users"
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            }`}
          >
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4" />
              <span>User Management</span>
            </div>
          </button>
          <button
            onClick={() => setActiveTab("payment-methods")}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === "payment-methods"
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            }`}
          >
            <div className="flex items-center space-x-2">
              <CreditCard className="h-4 w-4" />
              <span>Payment Methods</span>
            </div>
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === "pricing" && (
        <div className="space-y-6">
          {/* Building Selector */}
          <Card>
            <CardHeader>
              <CardTitle>Select Building</CardTitle>
              <CardDescription>
                Choose which building to configure pricing for
              </CardDescription>
            </CardHeader>
            <CardContent>
              <select
                value={selectedBuildingId || ""}
                onChange={(e) => setSelectedBuildingId(e.target.value || null)}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select a building...</option>
                {buildings.map((building) => (
                  <option key={building.id} value={building.id}>
                    {building.name}
                  </option>
                ))}
              </select>
            </CardContent>
          </Card>

          {/* Show message if no building selected */}
          {!selectedBuildingId && buildings.length > 0 && (
            <Card>
              <CardContent className="text-center py-8">
                <p className="text-gray-500">
                  Please select a building to configure its pricing settings.
                </p>
              </CardContent>
            </Card>
          )}

          {/* Settings Form - Only show when building is selected */}
          {selectedBuildingId && (
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Status Messages */}
              {error && (
                <div className="p-4 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
                  {error}
                </div>
              )}

              {success && (
                <div className="p-4 text-sm text-green-600 bg-green-50 border border-green-200 rounded-md">
                  {success}
                </div>
              )}

              {/* Utility Pricing */}
              <Card>
                <CardHeader>
                  <CardTitle>Utility Pricing</CardTitle>
                  <CardDescription>
                    Set the price per unit for water and electricity consumption
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Droplets className="h-4 w-4 text-blue-500" />
                        <label className="text-sm font-medium text-gray-700">
                          Water Price per Unit ($)
                        </label>
                      </div>
                      <Input
                        type="number"
                        step="0.0001"
                        placeholder="0.0000"
                        error={errors.waterPricePerUnit?.message}
                        {...register("waterPricePerUnit", {
                          valueAsNumber: true,
                        })}
                      />
                      <p className="text-xs text-gray-500">
                        Current rate: $
                        {currentPricing?.waterPricePerUnit
                          ? Number(currentPricing.waterPricePerUnit).toFixed(4)
                          : "0.0000"}{" "}
                        per unit
                      </p>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Zap className="h-4 w-4 text-yellow-500" />
                        <label className="text-sm font-medium text-gray-700">
                          Electricity Price per Unit ($)
                        </label>
                      </div>
                      <Input
                        type="number"
                        step="0.0001"
                        placeholder="0.0000"
                        error={errors.electricityPricePerUnit?.message}
                        {...register("electricityPricePerUnit", {
                          valueAsNumber: true,
                        })}
                      />
                      <p className="text-xs text-gray-500">
                        Current rate: $
                        {currentPricing?.electricityPricePerUnit
                          ? Number(
                              currentPricing.electricityPricePerUnit
                            ).toFixed(4)
                          : "0.0000"}{" "}
                        per unit
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Billing Configuration */}
              <Card>
                <CardHeader>
                  <CardTitle>Billing Configuration</CardTitle>
                  <CardDescription>
                    Control whether users can enter new billing entries
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">
                        Enable Billing Entry
                      </h3>
                      <p className="text-sm text-gray-500">
                        When enabled, users can submit new meter readings and
                        generate billing entries
                      </p>
                    </div>
                    <button
                      type="button"
                      onClick={toggleBilling}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                        billingEnabled ? "bg-blue-600" : "bg-gray-200"
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          billingEnabled ? "translate-x-6" : "translate-x-1"
                        }`}
                      />
                    </button>
                  </div>

                  <div className="mt-4 p-3 bg-gray-50 rounded-md">
                    <div className="flex items-center space-x-2">
                      {billingEnabled ? (
                        <ToggleRight className="h-5 w-5 text-green-600" />
                      ) : (
                        <ToggleLeft className="h-5 w-5 text-gray-400" />
                      )}
                      <span
                        className={`text-sm font-medium ${
                          billingEnabled ? "text-green-600" : "text-gray-500"
                        }`}
                      >
                        Billing entry is currently{" "}
                        {billingEnabled ? "enabled" : "disabled"}
                      </span>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      {billingEnabled
                        ? "Users can now submit meter readings and create billing entries"
                        : "Users cannot submit new billing entries at this time"}
                    </p>

                    {/* Validation Status */}
                    {billingEnabled && validationStatus && (
                      <div className="mt-3 p-2 bg-white rounded border">
                        <div className="flex items-center justify-between">
                          <span className="text-xs font-medium text-gray-700">
                            Billing Cycle Status
                          </span>
                          <button
                            type="button"
                            onClick={() =>
                              setShowValidationDetails(!showValidationDetails)
                            }
                            className="text-xs text-blue-600 hover:text-blue-800"
                          >
                            {showValidationDetails
                              ? "Hide Details"
                              : "Show Details"}
                          </button>
                        </div>
                        <div
                          className={`flex items-center space-x-1 mt-1 ${
                            validationStatus.canToggleOff
                              ? "text-green-600"
                              : "text-orange-600"
                          }`}
                        >
                          <span className="text-xs">
                            {validationStatus.canToggleOff ? "✅" : "⚠️"}
                          </span>
                          <span className="text-xs font-medium">
                            {validationStatus.canToggleOff
                              ? "Ready to close billing cycle"
                              : "Billing cycle incomplete"}
                          </span>
                        </div>

                        {showValidationDetails && (
                          <div className="mt-2 space-y-2">
                            {validationStatus.billingEnabledDate && (
                              <p className="text-xs text-gray-600">
                                <strong>Billing enabled since:</strong>{" "}
                                {formatDateThai(
                                  validationStatus.billingEnabledDate
                                )}
                              </p>
                            )}

                            {validationStatus.roomsWithoutBilling.length >
                              0 && (
                              <div className="text-xs">
                                <p className="font-medium text-red-600">
                                  Rooms without billing entries:
                                </p>
                                <p className="text-red-600">
                                  {validationStatus.roomsWithoutBilling.join(
                                    ", "
                                  )}
                                </p>
                              </div>
                            )}

                            {validationStatus.roomsWithOldBilling.length >
                              0 && (
                              <div className="text-xs">
                                <p className="font-medium text-orange-600">
                                  Rooms with outdated billing entries:
                                </p>
                                <p className="text-orange-600">
                                  {validationStatus.roomsWithOldBilling.join(
                                    ", "
                                  )}
                                </p>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Save Button */}
              <div className="flex justify-end">
                <Button
                  type="submit"
                  loading={saving}
                  disabled={saving}
                  className="flex items-center space-x-2"
                >
                  <Save className="h-4 w-4" />
                  <span>Save Settings</span>
                </Button>
              </div>
            </form>
          )}

          {/* Current Settings Summary */}
          {selectedBuildingId && currentPricing && (
            <Card>
              <CardHeader>
                <CardTitle>Current Settings Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                  <div className="p-4 bg-blue-50 rounded-lg">
                    <Droplets className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-blue-600">
                      ${Number(currentPricing.waterPricePerUnit).toFixed(4)}
                    </div>
                    <div className="text-sm text-gray-600">Water per unit</div>
                  </div>

                  <div className="p-4 bg-yellow-50 rounded-lg">
                    <Zap className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-yellow-600">
                      $
                      {Number(currentPricing.electricityPricePerUnit).toFixed(
                        4
                      )}
                    </div>
                    <div className="text-sm text-gray-600">
                      Electricity per unit
                    </div>
                  </div>

                  <div
                    className={`p-4 rounded-lg ${
                      currentPricing.billingEnabled
                        ? "bg-green-50"
                        : "bg-gray-50"
                    }`}
                  >
                    {currentPricing.billingEnabled ? (
                      <ToggleRight className="h-8 w-8 text-green-600 mx-auto mb-2" />
                    ) : (
                      <ToggleLeft className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                    )}
                    <div
                      className={`text-2xl font-bold ${
                        currentPricing.billingEnabled
                          ? "text-green-600"
                          : "text-gray-400"
                      }`}
                    >
                      {currentPricing.billingEnabled ? "ON" : "OFF"}
                    </div>
                    <div className="text-sm text-gray-600">Billing Entry</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* User Management Tab */}
      {activeTab === "users" && <UserManagement />}

      {/* Payment Methods Tab */}
      {activeTab === "payment-methods" && <PaymentMethodsManagement />}
    </div>
  );
};

export default AdminSettings;
