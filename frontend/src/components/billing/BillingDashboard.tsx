import React, { useState, useEffect } from "react";
import { Building } from "@/types";
import { buildingService } from "@/lib/services/buildings";
import { roomService } from "@/lib/services/rooms";
import { billingService } from "@/lib/services/billing";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import { formatCurrency, formatDate } from "@/lib/utils";
import {
  Building2,
  Home,
  DollarSign,
  Calendar,
  TrendingUp,
  FileText,
  Download,
} from "lucide-react";

interface BillingStats {
  totalRevenue: number;
  totalEntries: number;
  averageBill: number;
  lastBillingDate: string | null;
  buildingStats: Array<{
    building: Building;
    roomCount: number;
    totalRevenue: number;
    entryCount: number;
  }>;
}

const BillingDashboard: React.FC = () => {
  const [stats, setStats] = useState<BillingStats>({
    totalRevenue: 0,
    totalEntries: 0,
    averageBill: 0,
    lastBillingDate: null,
    buildingStats: [],
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchBillingStats();
  }, []);

  const fetchBillingStats = async () => {
    setLoading(true);
    setError(null);

    try {
      // Get all buildings
      const { data: buildings, error: buildingsError } =
        await buildingService.getBuildings();
      if (buildingsError || !buildings) {
        setError("Failed to fetch buildings");
        setLoading(false);
        return;
      }

      let totalRevenue = 0;
      let totalEntries = 0;
      let allBillingDates: string[] = [];
      const buildingStats: BillingStats["buildingStats"] = [];

      // Process each building
      for (const building of buildings) {
        const { data: rooms, error: roomsError } =
          await roomService.getRoomsByBuilding(building.id);
        if (roomsError || !rooms) continue;

        let buildingRevenue = 0;
        let buildingEntries = 0;

        // Process each room in the building
        for (const room of rooms) {
          const { data: entries, error: entriesError } =
            await billingService.getBillingEntriesByRoom(room.id);
          if (entriesError || !entries) continue;

          const roomRevenue = entries.reduce(
            (sum, entry) => sum + Number(entry.totalAmount),
            0
          );
          buildingRevenue += roomRevenue;
          buildingEntries += entries.length;
          totalRevenue += roomRevenue;
          totalEntries += entries.length;

          // Collect billing dates
          allBillingDates.push(...entries.map((entry) => entry.billingDate));
        }

        buildingStats.push({
          building,
          roomCount: rooms.length,
          totalRevenue: buildingRevenue,
          entryCount: buildingEntries,
        });
      }

      // Calculate stats
      const averageBill = totalEntries > 0 ? totalRevenue / totalEntries : 0;
      const lastBillingDate =
        allBillingDates.length > 0 ? allBillingDates.sort().reverse()[0] : null;

      setStats({
        totalRevenue,
        totalEntries,
        averageBill,
        lastBillingDate,
        buildingStats: buildingStats.sort(
          (a, b) => b.totalRevenue - a.totalRevenue
        ),
      });
    } catch (error) {
      setError("Failed to fetch billing statistics");
    }

    setLoading(false);
  };

  const exportAllBillingData = async () => {
    // This would export all billing data across all buildings
    // Implementation would be similar to the individual room export
    console.log("Exporting all billing data...");
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">Error: {error}</div>
        <Button onClick={fetchBillingStats}>Try Again</Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center space-x-2">
            <FileText className="h-6 w-6" />
            <span>Billing Dashboard</span>
          </h2>
          <p className="text-gray-600">
            Overview of all billing activities across properties
          </p>
        </div>
        <Button
          onClick={exportAllBillingData}
          variant="outline"
          className="flex items-center space-x-2"
        >
          <Download className="h-4 w-4" />
          <span>Export All Data</span>
        </Button>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-8 w-8 text-green-600" />
              <div>
                <div className="text-2xl font-bold text-green-600">
                  {formatCurrency(stats.totalRevenue)}
                </div>
                <div className="text-sm text-gray-500">Total Revenue</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <FileText className="h-8 w-8 text-blue-600" />
              <div>
                <div className="text-2xl font-bold text-blue-600">
                  {stats.totalEntries}
                </div>
                <div className="text-sm text-gray-500">Billing Entries</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-8 w-8 text-purple-600" />
              <div>
                <div className="text-2xl font-bold text-purple-600">
                  {formatCurrency(stats.averageBill)}
                </div>
                <div className="text-sm text-gray-500">Average Bill</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Calendar className="h-8 w-8 text-orange-600" />
              <div>
                <div className="text-2xl font-bold text-orange-600">
                  {stats.lastBillingDate
                    ? formatDate(stats.lastBillingDate)
                    : "Never"}
                </div>
                <div className="text-sm text-gray-500">Last Billing</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Building Performance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Building2 className="h-5 w-5" />
            <span>Building Performance</span>
          </CardTitle>
          <CardDescription>
            Revenue and billing activity by building
          </CardDescription>
        </CardHeader>
        <CardContent>
          {stats.buildingStats.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No billing data available
            </div>
          ) : (
            <div className="space-y-4">
              {stats.buildingStats.map(
                ({ building, roomCount, totalRevenue, entryCount }) => (
                  <div
                    key={building.id}
                    className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <Building2 className="h-5 w-5 text-blue-600" />
                          <h3 className="text-lg font-semibold text-gray-900">
                            {building.name}
                          </h3>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                          <div className="flex items-center space-x-1">
                            <Home className="h-4 w-4 text-gray-500" />
                            <span className="text-gray-600">Rooms:</span>
                            <span className="font-medium">{roomCount}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <FileText className="h-4 w-4 text-gray-500" />
                            <span className="text-gray-600">Entries:</span>
                            <span className="font-medium">{entryCount}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <TrendingUp className="h-4 w-4 text-gray-500" />
                            <span className="text-gray-600">
                              Avg per Entry:
                            </span>
                            <span className="font-medium">
                              {entryCount > 0
                                ? formatCurrency(totalRevenue / entryCount)
                                : "$0.00"}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-2xl font-bold text-green-600">
                          {formatCurrency(totalRevenue)}
                        </div>
                        <div className="text-sm text-gray-500">
                          Total Revenue
                        </div>
                      </div>
                    </div>
                  </div>
                )
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Common billing management tasks</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button
              variant="outline"
              className="h-20 flex flex-col items-center justify-center space-y-2"
              onClick={() => (window.location.href = "/admin")}
            >
              <DollarSign className="h-6 w-6" />
              <span>Update Pricing</span>
            </Button>

            <Button
              variant="outline"
              className="h-20 flex flex-col items-center justify-center space-y-2"
              onClick={exportAllBillingData}
            >
              <Download className="h-6 w-6" />
              <span>Export Reports</span>
            </Button>

            <Button
              variant="outline"
              className="h-20 flex flex-col items-center justify-center space-y-2"
              onClick={fetchBillingStats}
            >
              <TrendingUp className="h-6 w-6" />
              <span>Refresh Stats</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default BillingDashboard;
