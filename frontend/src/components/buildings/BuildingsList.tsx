import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth';
import { buildingService } from '@/lib/services/buildings';
import { Building } from '@/types';
import BuildingCard from './BuildingCard';
import CreateBuildingForm from './CreateBuildingForm';
import Button from '@/components/ui/Button';
import { Plus, Building2 } from 'lucide-react';

const BuildingsList: React.FC = () => {
  const { isAdmin } = useAuth();
  const router = useRouter();
  const [buildings, setBuildings] = useState<Building[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);

  useEffect(() => {
    fetchBuildings();
  }, []);

  const fetchBuildings = async () => {
    setLoading(true);
    const { data, error } = await buildingService.getBuildings();
    
    if (error) {
      setError(error);
    } else if (data) {
      setBuildings(data);
    }
    
    setLoading(false);
  };

  const handleSelectBuilding = (building: Building) => {
    router.push(`/buildings/${building.id}`);
  };

  const handleCreateSuccess = (newBuilding: Building) => {
    setBuildings(prev => [newBuilding, ...prev]);
  };

  const handleEditBuilding = (building: Building) => {
    // TODO: Implement edit functionality
    console.log('Edit building:', building);
  };

  const handleDeleteBuilding = async (building: Building) => {
    if (!confirm(`Are you sure you want to delete "${building.name}"?`)) {
      return;
    }

    const { error } = await buildingService.deleteBuilding(building.id);
    
    if (error) {
      alert(`Failed to delete building: ${error}`);
    } else {
      setBuildings(prev => prev.filter(b => b.id !== building.id));
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">Error loading buildings: {error}</div>
        <Button onClick={fetchBuildings}>Try Again</Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Buildings</h2>
          <p className="text-gray-600">Manage your apartment buildings</p>
        </div>
        {isAdmin && (
          <Button
            onClick={() => setShowCreateForm(true)}
            className="flex items-center space-x-2"
          >
            <Plus size={20} />
            <span>Add Building</span>
          </Button>
        )}
      </div>

      {/* Buildings Grid */}
      {buildings.length === 0 ? (
        <div className="text-center py-12">
          <Building2 className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No buildings yet</h3>
          <p className="text-gray-600 mb-4">
            {isAdmin 
              ? 'Get started by creating your first building.'
              : 'No buildings have been created yet.'
            }
          </p>
          {isAdmin && (
            <Button onClick={() => setShowCreateForm(true)}>
              Create Your First Building
            </Button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {buildings.map((building) => (
            <BuildingCard
              key={building.id}
              building={building}
              onSelect={handleSelectBuilding}
              onEdit={isAdmin ? handleEditBuilding : undefined}
              onDelete={isAdmin ? handleDeleteBuilding : undefined}
              isAdmin={isAdmin}
            />
          ))}
        </div>
      )}

      {/* Create Building Form */}
      <CreateBuildingForm
        isOpen={showCreateForm}
        onClose={() => setShowCreateForm(false)}
        onSuccess={handleCreateSuccess}
      />
    </div>
  );
};

export default BuildingsList;
