import React from "react";
import { Customer } from "@/types";
import { Card, CardContent } from "@/components/ui/Card";
import { User, Phone, CreditCard } from "lucide-react";

interface CustomerCardProps {
  customer: Customer;
  onClick: (customer: Customer) => void;
}

const CustomerCard: React.FC<CustomerCardProps> = ({ customer, onClick }) => {
  return (
    <Card
      className="hover:shadow-lg transition-shadow cursor-pointer w-full"
      onClick={() => onClick(customer)}
    >
      <CardContent className="p-4">
        <div className="flex flex-col space-y-3">
          {/* Name (English) */}
          <div className="flex items-center space-x-2">
            <User className="h-4 w-4 text-blue-600 flex-shrink-0" />
            <span className="font-medium text-gray-900">{customer.nameEn}</span>
          </div>

          {/* Name (Thai) */}
          {customer.nameTh && (
            <div className="flex items-center space-x-2">
              <div className="h-4 w-4 flex-shrink-0"></div>{" "}
              {/* Spacer for alignment */}
              <span className="text-gray-600">{customer.nameTh}</span>
            </div>
          )}

          {/* ID Number */}
          <div className="flex items-center space-x-2">
            <CreditCard className="h-4 w-4 text-gray-500 flex-shrink-0" />
            <span className="text-gray-700">ID: {customer.idNumber}</span>
          </div>

          {/* Phone Number */}
          <div className="flex items-center space-x-2">
            <Phone className="h-4 w-4 text-gray-500 flex-shrink-0" />
            <span className="text-gray-700">{customer.phoneNumber}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default CustomerCard;
