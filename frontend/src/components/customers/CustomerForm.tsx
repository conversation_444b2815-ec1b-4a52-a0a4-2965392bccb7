import React, { useState } from "react";
import { CreateCustomerData } from "@/types";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import { Label } from "@/components/ui/Label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import { UserPlus, X } from "lucide-react";

interface CustomerFormProps {
  buildingId: string;
  onSubmit: (customerData: CreateCustomerData) => Promise<void>;
  loading?: boolean;
  error?: string | null;
  success?: string | null;
  showCloseForm?: boolean;
  onCloseForm?: () => void;
}

const CustomerForm: React.FC<CustomerFormProps> = ({
  buildingId,
  onSubmit,
  loading = false,
  error = null,
  success = null,
  showCloseForm = false,
  onCloseForm,
}) => {
  const [formData, setFormData] = useState<CreateCustomerData>({
    nameEn: "",
    nameTh: "",
    idNumber: "",
    phoneNumber: "",
    address: "",
    buildingId,
  });

  const [validationErrors, setValidationErrors] = useState<{
    [key: string]: string;
  }>({});

  const validateForm = (): boolean => {
    const errors: { [key: string]: string } = {};

    // Required fields
    if (!formData.nameEn.trim()) {
      errors.nameEn = "English name is required";
    }

    if (!formData.idNumber.trim()) {
      errors.idNumber = "ID number is required";
    } else if (formData.idNumber.length < 5) {
      errors.idNumber = "ID number must be at least 5 characters";
    }

    if (!formData.phoneNumber.trim()) {
      errors.phoneNumber = "Phone number is required";
    } else if (!/^[\d\-\+\(\)\s]+$/.test(formData.phoneNumber)) {
      errors.phoneNumber = "Please enter a valid phone number";
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    await onSubmit(formData);

    // Reset form on success (if no error)
    if (!error) {
      setFormData({
        nameEn: "",
        nameTh: "",
        idNumber: "",
        phoneNumber: "",
        address: "",
        buildingId,
      });
      setValidationErrors({});
    }
  };

  const handleInputChange = (
    field: keyof CreateCustomerData,
    value: string
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));

    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  return (
    <Card>
      <div className="flex justify-between">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <UserPlus className="h-5 w-5" />
            <span>Add New Customer</span>
          </CardTitle>
        </CardHeader>
        {showCloseForm && (
          <button
            type="button"
            onClick={onCloseForm}
            className="text-gray-400 hover:text-gray-600 transition-colors pr-4"
          >
            <X size={20} className="sm:w-6 sm:h-6" />
          </button>
        )}
      </div>

      <CardContent>
        {/* Success/Error Messages */}
        {success && (
          <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md">
            <p className="text-sm text-green-600">{success}</p>
          </div>
        )}

        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* English Name */}
            <div>
              <Label htmlFor="nameEn">Name (English) *</Label>
              <Input
                id="nameEn"
                value={formData.nameEn}
                onChange={(e) => handleInputChange("nameEn", e.target.value)}
                placeholder="Enter English name"
                className={validationErrors.nameEn ? "border-red-500" : ""}
              />
              {validationErrors.nameEn && (
                <p className="text-sm text-red-600 mt-1">
                  {validationErrors.nameEn}
                </p>
              )}
            </div>

            {/* Thai Name */}
            <div>
              <Label htmlFor="nameTh">Name (Thai)</Label>
              <Input
                id="nameTh"
                value={formData.nameTh || ""}
                onChange={(e) => handleInputChange("nameTh", e.target.value)}
                placeholder="Enter Thai name (optional)"
              />
            </div>

            {/* ID Number */}
            <div>
              <Label htmlFor="idNumber">ID Number *</Label>
              <Input
                id="idNumber"
                value={formData.idNumber}
                onChange={(e) => handleInputChange("idNumber", e.target.value)}
                placeholder="Enter ID number"
                className={validationErrors.idNumber ? "border-red-500" : ""}
              />
              {validationErrors.idNumber && (
                <p className="text-sm text-red-600 mt-1">
                  {validationErrors.idNumber}
                </p>
              )}
            </div>

            {/* Phone Number */}
            <div>
              <Label htmlFor="phoneNumber">Phone Number *</Label>
              <Input
                id="phoneNumber"
                value={formData.phoneNumber}
                onChange={(e) =>
                  handleInputChange("phoneNumber", e.target.value)
                }
                placeholder="Enter phone number"
                className={validationErrors.phoneNumber ? "border-red-500" : ""}
              />
              {validationErrors.phoneNumber && (
                <p className="text-sm text-red-600 mt-1">
                  {validationErrors.phoneNumber}
                </p>
              )}
            </div>
          </div>

          {/* Address */}
          <div>
            <Label htmlFor="address">Address</Label>
            <textarea
              id="address"
              value={formData.address || ""}
              onChange={(e) => handleInputChange("address", e.target.value)}
              placeholder="Enter address (optional)"
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Submit Button */}
          <div className="flex justify-end">
            <Button
              type="submit"
              disabled={loading}
              className="flex items-center space-x-2"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Creating...</span>
                </>
              ) : (
                <>
                  <UserPlus className="h-4 w-4" />
                  <span>Add Customer</span>
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default CustomerForm;
