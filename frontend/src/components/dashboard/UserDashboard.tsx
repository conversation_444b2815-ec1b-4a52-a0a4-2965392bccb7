import React, { useState, useEffect } from "react";
import { useAuth } from "@/lib/auth";
import { userManagementAPI } from "@/lib/api";
import { Building } from "@/types";
import Button from "@/components/ui/Button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";
import {
  Building as BuildingIcon,
  Users,
  Home,
  ArrowRight,
} from "lucide-react";
import Link from "next/link";

const UserDashboard: React.FC = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [buildings, setBuildings] = useState<Building[]>([]);

  useEffect(() => {
    fetchMyBuildings();
  }, []);

  const fetchMyBuildings = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await userManagementAPI.getMyBuildings();
      setBuildings(response);
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error && "response" in err
          ? (err as { response?: { data?: { message?: string } } }).response
              ?.data?.message
          : "Failed to fetch your buildings";
      setError(errorMessage || "Failed to fetch your buildings");
    }

    setLoading(false);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
        {error}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">
          Welcome, {user?.username}
        </h1>
        <p className="text-gray-600">
          Manage billing for your assigned buildings
        </p>
      </div>

      {/* Buildings Grid */}
      {buildings.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <BuildingIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No Buildings Assigned
            </h3>
            <p className="text-gray-500">
              You don't have access to any buildings yet. Contact your
              administrator to get access.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {buildings.map((building) => (
            <Card
              key={building.id}
              className="hover:shadow-lg transition-shadow"
            >
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <BuildingIcon className="h-5 w-5 text-blue-600" />
                    <CardTitle className="text-lg">{building.name}</CardTitle>
                  </div>
                </div>
                <CardDescription>
                  Building managed by {building.user?.email}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Building Stats */}
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <Home className="h-6 w-6 text-blue-600 mx-auto mb-1" />
                    <div className="text-2xl font-bold text-blue-600">
                      {building._count?.rooms || 0}
                    </div>
                    <div className="text-xs text-gray-600">Rooms</div>
                  </div>
                  <div className="p-3 bg-green-50 rounded-lg">
                    <Users className="h-6 w-6 text-green-600 mx-auto mb-1" />
                    <div className="text-2xl font-bold text-green-600">
                      {building.rooms?.filter(
                        (room) => room.status === "AVAILABLE"
                      ).length || 0}
                    </div>
                    <div className="text-xs text-gray-600">Available</div>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex space-x-2">
                  <Link href={`/buildings/${building.id}`} className="flex-1">
                    <Button
                      variant="outline"
                      className="w-full flex items-center justify-center space-x-2"
                    >
                      <span>View Details</span>
                      <ArrowRight className="h-4 w-4" />
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default UserDashboard;
