import React, { useState, useEffect, useCallback } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { roomService } from "@/lib/services/rooms";
import { customersService } from "@/lib/services/customers";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import Modal from "@/components/ui/Modal";
import { IRoomCard, Customer, CreateCustomerData } from "@/types";
import { Plus, Trash2, Users } from "lucide-react";
import CustomerForm from "../customers/CustomerForm";

const additionalServiceSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, "Service name is required"),
  price: z.number().min(0, "Price must be positive"),
  description: z.string().optional(),
});

const editRoomSchema = z
  .object({
    roomNumber: z.string().min(1, "Room number is required"),
    monthlyRent: z.number().min(0, "Monthly rent must be positive"),
    status: z.enum(["AVAILABLE", "OCCUPIED", "MAINTENANCE"]),
    additionalServices: z.array(additionalServiceSchema).optional(),
    selectedCustomers: z.array(z.string()).optional(),
  })
  .refine(
    (data) => {
      // If status is OCCUPIED, at least one customer must be selected
      if (data.status === "OCCUPIED") {
        return data.selectedCustomers && data.selectedCustomers.length > 0;
      }
      return true;
    },
    {
      message:
        "At least one customer must be selected when room status is OCCUPIED",
      path: ["selectedCustomers"],
    }
  );

type EditRoomFormData = z.infer<typeof editRoomSchema>;

interface EditRoomFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  room: IRoomCard | null;
}

const EditRoomForm: React.FC<EditRoomFormProps> = ({
  isOpen,
  onClose,
  onSuccess,
  room,
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [originalServices, setOriginalServices] = useState<any[]>([]);

  // Customer management state
  const [availableCustomers, setAvailableCustomers] = useState<Customer[]>([]);
  const [currentCustomers, setCurrentCustomers] = useState<Customer[]>([]);
  const [loadingCustomers, setLoadingCustomers] = useState(false);
  const [customerSearchTerm, setCustomerSearchTerm] = useState("");
  const [showAddCustomer, setShowAddCustomer] = useState(false);
  const [creatingCustomer, setCreatingCustomer] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    control,
    setValue,
    watch,
  } = useForm<EditRoomFormData>({
    resolver: zodResolver(editRoomSchema),
    defaultValues: {
      additionalServices: [],
      selectedCustomers: [],
    },
  });

  const watchedStatus = watch("status");

  // Filter customers based on search term
  const filteredCustomers = availableCustomers.filter((customer) =>
    customer.nameEn.toLowerCase().includes(customerSearchTerm.toLowerCase())
  );

  const { fields, append, remove } = useFieldArray({
    control,
    name: "additionalServices",
  });

  // Populate form when room changes
  useEffect(() => {
    const getAdditionalServices = async () => {
      if (!room) return;

      const { data, error } = await roomService.findAdditionalServices(room.id);

      if (error) {
        setError(error);
      } else if (data) {
        return data;
      }
    };

    const populateForm = async () => {
      if (room && isOpen) {
        setValue("roomNumber", room.roomNumber);
        setValue("monthlyRent", room.monthlyRent);
        setValue("status", room.status);

        // Set additional services
        const services = await getAdditionalServices();
        if (services) {
          setOriginalServices(services); // Store original services for comparison
          setValue("additionalServices", services);

          // Clear and repopulate field array
          remove(); // Remove all existing fields
          services.forEach((service) => {
            append({
              id: service.id, // Include the ID for tracking
              name: service.name,
              price: service.price,
              description: service.description || "",
            });
          });
        }
      }
    };

    populateForm();
  }, [room, isOpen, setValue, append, remove]);

  const fetchCustomersData = useCallback(async () => {
    if (!room) return;

    setLoadingCustomers(true);
    try {
      // Fetch all customers for the building
      const { data: buildingCustomers, error: customersError } =
        await customersService.getByBuilding(room.buildingId);

      if (customersError) {
        console.error("Error fetching customers:", customersError);
        return;
      }

      // Fetch current customers assigned to this room
      const { data: roomCustomers, error: roomCustomersError } =
        await customersService.getCustomersByRoomId(room.id);

      if (roomCustomersError) {
        console.error("Error fetching room customers:", roomCustomersError);
        return;
      }

      if (roomCustomers) {
        const currentCustomerList = roomCustomers.flatMap((rc) =>
          rc.customer ? [rc.customer] : []
        );
        setCurrentCustomers(currentCustomerList);

        // Set selected customer IDs in form
        const customerIds = currentCustomerList.map((c) => c.id);
        setValue("selectedCustomers", customerIds);

        //sort by current customer
        if (buildingCustomers) {
          sortAvailableCustomers(buildingCustomers, customerIds);
        }
      }
    } catch (error) {
      console.error("Error fetching customer data:", error);
    } finally {
      setLoadingCustomers(false);
    }
  }, [room, setValue]);

  const sortAvailableCustomers = (
    buildingCustomers: Customer[],
    customerIds: string[]
  ) => {
    const sortedCustomers = [...buildingCustomers].sort((a, b) => {
      if (customerIds.includes(a.id) && !customerIds.includes(b.id)) {
        return -1;
      }
      if (!customerIds.includes(a.id) && customerIds.includes(b.id)) {
        return 1;
      }
      return 0;
    });
    setAvailableCustomers(sortedCustomers);
  };

  // Fetch customers when modal opens
  useEffect(() => {
    if (isOpen && room) {
      fetchCustomersData();
    }
  }, [isOpen, room, fetchCustomersData]);

  const onSubmit = async (data: EditRoomFormData) => {
    if (!room) return;

    setLoading(true);
    setError(null);

    try {
      // First update the room
      const { data: updatedRoom, error: updateError } =
        await roomService.updateRoomWithServices(
          room.id,
          {
            roomNumber: data.roomNumber,
            monthlyRent: data.monthlyRent,
            status: data.status,
          },
          originalServices,
          data.additionalServices || []
        );

      if (updateError) {
        setError(updateError);
        setLoading(false);
        return;
      }

      // Handle customer assignments if status is OCCUPIED
      if (data.status === "OCCUPIED" && data.selectedCustomers) {
        await handleCustomerAssignments(data.selectedCustomers);
      } else if (data.status == "AVAILABLE") {
        // If status is AVAILABLE, remove all customer assignments
        await handleCustomerAssignments([]);
      }

      if (updatedRoom) {
        onSuccess();
        onClose();
      }
    } catch (error) {
      console.error("Error updating room:", error);
      setError("Failed to update room");
    } finally {
      setLoading(false);
    }
  };

  const handleCustomerAssignments = async (newCustomerIds: string[]) => {
    const currentCustomerIds = currentCustomers.map((c) => c.id);

    // Find customers to remove (in current but not in new)
    const customersToRemove = currentCustomerIds.filter(
      (id) => !newCustomerIds.includes(id)
    );

    // Find customers to add (in new but not in current)
    const customersToAdd = newCustomerIds.filter(
      (id) => !currentCustomerIds.includes(id)
    );

    // Remove customers
    for (const customerId of customersToRemove) {
      try {
        await customersService.removeRoomAssignment(customerId, room!.id);
      } catch (error) {
        console.error(`Error removing customer ${customerId}:`, error);
      }
    }

    // Add customers
    for (const customerId of customersToAdd) {
      try {
        await customersService.assignRoom(customerId, room!.id);
      } catch (error) {
        console.error(`Error assigning customer ${customerId}:`, error);
      }
    }
  };

  const handleClose = () => {
    reset();
    setError(null);
    setCustomerSearchTerm(""); // Clear search term
    onClose();
  };

  const addService = () => {
    append({
      id: `temp_${Date.now()}`, // Temporary ID for new services
      name: "",
      price: 0,
      description: "",
    });
  };

  const handleShowCustomerForm = () => {
    setShowAddCustomer(true);
  };

  const handleCloseCustomerForm = () => {
    setShowAddCustomer(false);
  };

  const clearMessages = () => {
    setError(null);
    setSuccess(null);
  };

  const handleCreateCustomer = async (customerData: CreateCustomerData) => {
    setCreatingCustomer(true);
    setError(null);
    setSuccess(null);

    try {
      const { error } = await customersService.create(customerData);
      if (error) {
        setError(error);
      } else {
        setSuccess("Customer created successfully");
        fetchCustomersData(); // Refresh the list
      }
    } catch {
      setError("Failed to create customer");
    } finally {
      setCreatingCustomer(false);
    }
  };

  if (!room) return null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={`Edit Room ${room.roomNumber}`}
      size="lg"
    >
      {success && (
        <div className="p-3 bg-green-50 border border-green-200 rounded-md mb-2">
          <p className="text-sm text-green-600">{success}</p>
          <button
            onClick={clearMessages}
            className="text-green-600 hover:text-green-700 text-xs underline ml-2"
          >
            Dismiss
          </button>
        </div>
      )}
      {error && (
        <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md mb-2">
          {error}
        </div>
      )}
      {/* Add Customer Form - Outside main form to prevent conflicts */}
      {showAddCustomer && (
        <div className="mb-4">
          <CustomerForm
            buildingId={room.buildingId}
            onSubmit={handleCreateCustomer}
            loading={creatingCustomer}
            error={null} // We handle errors globally
            success={null} // We handle success globally
            showCloseForm
            onCloseForm={handleCloseCustomerForm}
          />
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Basic Room Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="Room Number"
            placeholder="e.g., 101, A1, etc."
            error={errors.roomNumber?.message}
            {...register("roomNumber")}
          />

          <Input
            label="Monthly Rent ($)"
            type="number"
            step="0.01"
            placeholder="0.00"
            error={errors.monthlyRent?.message}
            {...register("monthlyRent", { valueAsNumber: true })}
          />
        </div>

        {/* Room Status */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Room Status
          </label>
          <select
            {...register("status")}
            className="w-full px-3 py-2 text-gray-900 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="AVAILABLE">Available</option>
            <option value="OCCUPIED">Occupied</option>
            <option value="MAINTENANCE">Maintenance</option>
          </select>
          {errors.status && (
            <p className="mt-1 text-sm text-red-600">{errors.status.message}</p>
          )}
        </div>

        {/* Customer Selection - Only show when status is OCCUPIED */}
        {watchedStatus === "OCCUPIED" && (
          <div>
            <div className="flex justify-between items-center mb-2 text-gray-700">
              <div className="flex flex-col mb-2">
                <label className="block text-sm font-medium text-gray-700">
                  <Users className="inline h-4 w-4 mr-1" />
                  Select Customers
                </label>
                {availableCustomers.length > 0 && (
                  <span className="text-sm text-gray-500">
                    {customerSearchTerm
                      ? `${filteredCustomers.length} of ${availableCustomers.length} customers`
                      : `${availableCustomers.length} customers`}
                  </span>
                )}
              </div>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleShowCustomerForm}
                className="flex items-center space-x-1"
              >
                <Plus className="h-4 w-4 " />
                <span>Add Customer</span>
              </Button>
            </div>

            {loadingCustomers ? (
              <div className="flex items-center justify-center py-4 border border-gray-300 rounded-md">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <span className="ml-2 text-gray-600">Loading customers...</span>
              </div>
            ) : (
              <div className="space-y-3">
                {/* Search Input */}
                {availableCustomers.length > 0 && (
                  <input
                    type="text"
                    placeholder="Search customers by name..."
                    value={customerSearchTerm}
                    onChange={(e) => setCustomerSearchTerm(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-700"
                  />
                )}

                {/* Customer List */}
                <div
                  className="border border-gray-300 rounded-md overflow-y-auto"
                  style={{ maxHeight: "240px" }} // Height for approximately 5 customers (48px each)
                >
                  {availableCustomers.length === 0 ? (
                    <div className="p-4 text-center text-gray-500">
                      No customers available in this building
                    </div>
                  ) : filteredCustomers.length === 0 ? (
                    <div className="p-4 text-center text-gray-500">
                      No customers found matching &quot;{customerSearchTerm}
                      &quot;
                    </div>
                  ) : (
                    <div className="p-2 space-y-2">
                      {filteredCustomers.map((customer) => (
                        <label
                          key={customer.id}
                          className="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded cursor-pointer"
                        >
                          <input
                            type="checkbox"
                            value={customer.id}
                            {...register("selectedCustomers")}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <div className="flex-1">
                            <div className="font-medium text-gray-900">
                              {customer.nameEn}
                            </div>
                            {customer.nameTh && (
                              <div className="text-sm text-gray-600">
                                {customer.nameTh}
                              </div>
                            )}
                            <div className="text-sm text-gray-500">
                              ID: {customer.idNumber} | Phone:{" "}
                              {customer.phoneNumber}
                            </div>
                          </div>
                        </label>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )}
            {errors.selectedCustomers && (
              <p className="mt-1 text-sm text-red-600">
                {errors.selectedCustomers.message}
              </p>
            )}
          </div>
        )}

        {/* Additional Services */}
        <div>
          <div className="flex justify-between items-center mb-4 text-gray-700">
            <label className="block text-sm font-medium">
              Additional Services
            </label>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addService}
              className="flex items-center space-x-1"
            >
              <Plus className="h-4 w-4 " />
              <span>Add Service</span>
            </Button>
          </div>

          {fields.length === 0 ? (
            <p className="text-sm text-gray-500 text-center py-4 border-2 border-dashed border-gray-300 rounded-md">
              No additional services. Click &quot;Add Service&quot; to add one.
            </p>
          ) : (
            <div className="space-y-4">
              {fields.map((field, index) => (
                <div
                  key={field.id}
                  className="p-4 border border-gray-200 rounded-md space-y-3"
                >
                  <div className="flex justify-between items-center">
                    <h4 className="text-sm font-medium text-gray-700">
                      Service {index + 1}
                    </h4>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => remove(index)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <Input
                      label="Service Name"
                      placeholder="e.g., Parking, Internet"
                      error={errors.additionalServices?.[index]?.name?.message}
                      {...register(`additionalServices.${index}.name`)}
                    />

                    <Input
                      label="Price ($)"
                      type="number"
                      step="0.01"
                      placeholder="0.00"
                      error={errors.additionalServices?.[index]?.price?.message}
                      {...register(`additionalServices.${index}.price`, {
                        valueAsNumber: true,
                      })}
                    />
                  </div>

                  <Input
                    label="Description (Optional)"
                    placeholder="Service description"
                    error={
                      errors.additionalServices?.[index]?.description?.message
                    }
                    {...register(`additionalServices.${index}.description`)}
                  />
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-4 border-t">
          <Button
            type="button"
            variant="outline"
            className="text-gray-900"
            onClick={handleClose}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={loading}>
            {loading ? "Updating..." : "Update Room"}
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default EditRoomForm;
