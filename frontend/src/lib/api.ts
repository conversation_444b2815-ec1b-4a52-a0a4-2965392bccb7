import axios from "axios";

const API_URL = process.env.BACKEND_API_URL || "http://localhost:3001";

// Create axios instance
const api = axios.create({
  baseURL: API_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  if (typeof window !== "undefined") {
    const token = localStorage.getItem("auth_token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
  }
  return config;
});

// Handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Clear token and redirect to login
      if (typeof window !== "undefined") {
        localStorage.removeItem("auth_token");
        localStorage.removeItem("user");
        window.location.href = "/auth";
      }
    }
    return Promise.reject(error);
  }
);

export default api;

// Auth API
export const authAPI = {
  login: async (identifier: string, password: string) => {
    const response = await api.post("/auth/login", { identifier, password });
    return response.data;
  },

  register: async (email: string, username: string, password: string) => {
    const response = await api.post("/auth/register", {
      email,
      username,
      password,
    });
    return response.data;
  },

  getProfile: async () => {
    const response = await api.get("/auth/me");
    return response.data;
  },
};

// Buildings API
export const buildingsAPI = {
  getAll: async () => {
    const response = await api.get("/buildings");
    return response.data;
  },

  getById: async (id: string) => {
    const response = await api.get(`/buildings/${id}`);
    return response.data;
  },

  create: async (data: { name: string }) => {
    const response = await api.post("/buildings", data);
    return response.data;
  },

  update: async (id: string, data: { name: string }) => {
    const response = await api.patch(`/buildings/${id}`, data);
    return response.data;
  },

  delete: async (id: string) => {
    const response = await api.delete(`/buildings/${id}`);
    return response.data;
  },
};

// Rooms API
export const roomsAPI = {
  getRoomNumbersAndStatus: async (buildingId: string) => {
    const response = await api.get(
      `/buildings/${buildingId}/rooms/number/status`
    );
    return response.data;
  },

  getAll: async (buildingId: string) => {
    const response = await api.get(`/buildings/${buildingId}/rooms`);
    return response.data;
  },

  getById: async (id: string) => {
    const response = await api.get(`/rooms/${id}`);
    return response.data;
  },

  findAdditionalServices: async (id: string) => {
    const response = await api.get(`/rooms/services/${id}`);
    return response.data;
  },

  create: async (buildingId: string, data: Record<string, unknown>) => {
    const response = await api.post(`/buildings/${buildingId}/rooms`, data);
    return response.data;
  },

  update: async (
    buildingId: string,
    id: string,
    data: Record<string, unknown>
  ) => {
    const response = await api.patch(
      `/buildings/${buildingId}/rooms/${id}`,
      data
    );
    return response.data;
  },

  delete: async (buildingId: string, id: string) => {
    const response = await api.delete(`/buildings/${buildingId}/rooms/${id}`);
    return response.data;
  },

  // Direct room operations (without building ID in path)
  updateDirect: async (id: string, data: Record<string, unknown>) => {
    const response = await api.patch(`/rooms/${id}`, data);
    return response.data;
  },

  deleteDirect: async (id: string) => {
    const response = await api.delete(`/rooms/${id}`);
    return response.data;
  },
};

// Billing API
export const billingAPI = {
  getEntries: async (roomId?: string) => {
    const params = roomId ? { roomId } : {};
    const response = await api.get("/billing/entries", { params });
    return response.data;
  },

  createEntry: async (roomId: string, data: Record<string, unknown>) => {
    const response = await api.post(`/billing/rooms/${roomId}/entries`, data);
    return response.data;
  },

  getBillingCycles: async (buildingId: string) => {
    const response = await api.get(`/billing/buildings/${buildingId}/cycles`);
    return response.data;
  },

  getBillingHistoryByCycleId: async (buildingId: string, cycleId: string) => {
    const response = await api.get(
      `/billing/buildings/${buildingId}/cycle/${cycleId}`
    );
    return response.data;
  },

  getBuildingPricing: async (buildingId: string) => {
    const response = await api.get(`/billing/buildings/${buildingId}/pricing`);
    return response.data;
  },

  updateBuildingPricing: async (
    buildingId: string,
    data: Record<string, unknown>
  ) => {
    const response = await api.patch(
      `/billing/buildings/${buildingId}/pricing`,
      data
    );
    return response.data;
  },

  getBillingToggleValidation: async (buildingId: string) => {
    const response = await api.get(
      `/billing/buildings/${buildingId}/toggle-validation`
    );
    return response.data;
  },

  // Payment Management API
  addPayment: async (
    billingId: string,
    data: { amount: number; paymentDate: string; paymentMethodName: string }
  ) => {
    const response = await api.post(`/billing/${billingId}/payments`, data);
    return response.data;
  },

  getPaymentHistory: async (billingId: string) => {
    const response = await api.get(`/billing/${billingId}/payments`);
    return response.data;
  },

  getCurrentBillingCycle: async (buildingId: string) => {
    const response = await api.get(
      `/billing/buildings/${buildingId}/current-cycle`
    );
    return response.data;
  },
};

// Payment Methods API
export const paymentMethodsAPI = {
  getAll: async () => {
    const response = await api.get("/payment-methods");
    return response.data;
  },

  getActive: async () => {
    const response = await api.get("/payment-methods/active");
    return response.data;
  },

  getById: async (id: string) => {
    const response = await api.get(`/payment-methods/${id}`);
    return response.data;
  },

  create: async (data: {
    name: string;
    description?: string;
    isActive?: boolean;
  }) => {
    const response = await api.post("/payment-methods", data);
    return response.data;
  },

  update: async (
    id: string,
    data: {
      name?: string;
      description?: string;
      isActive?: boolean;
    }
  ) => {
    const response = await api.patch(`/payment-methods/${id}`, data);
    return response.data;
  },

  delete: async (id: string) => {
    const response = await api.delete(`/payment-methods/${id}`);
    return response.data;
  },
};

// Customers API
export const customersAPI = {
  getByBuilding: async (buildingId: string) => {
    const response = await api.get(`/customers?buildingId=${buildingId}`);
    return response.data;
  },

  getById: async (id: string) => {
    const response = await api.get(`/customers/${id}`);
    return response.data;
  },

  create: async (data: {
    nameEn: string;
    nameTh?: string;
    idNumber: string;
    phoneNumber: string;
    address?: string;
    buildingId: string;
  }) => {
    const response = await api.post("/customers", data);
    return response.data;
  },

  update: async (
    id: string,
    data: {
      nameEn?: string;
      nameTh?: string;
      idNumber?: string;
      phoneNumber?: string;
      address?: string;
      buildingId?: string;
    }
  ) => {
    const response = await api.patch(`/customers/${id}`, data);
    return response.data;
  },

  delete: async (id: string) => {
    const response = await api.delete(`/customers/${id}`);
    return response.data;
  },

  getCustomersByRoomId: async (roomId: string) => {
    const response = await api.get(`/customers/rooms/${roomId}`);
    return response.data;
  },

  getRoomAssignments: async (customerId: string) => {
    const response = await api.get(
      `/customers/${customerId}/rooms/assignments`
    );
    return response.data;
  },

  assignRoom: async (customerId: string, roomId: string) => {
    const response = await api.post(`/customers/rooms/assign`, {
      roomId,
      customerId,
    });
    return response.data;
  },

  removeRoomAssignment: async (customerId: string, roomId: string) => {
    const response = await api.delete(
      `/customers/${customerId}/rooms/${roomId}`
    );
    return response.data;
  },
};

// User Management API
export const userManagementAPI = {
  createUserForBuilding: async (
    buildingId: string,
    customPassword?: string,
    customUsername?: string
  ) => {
    const payload = {
      customPassword,
      customUsername,
    };
    console.log("API sending payload:", payload);

    const response = await api.post(
      `/user-management/buildings/${buildingId}/users`,
      payload
    );
    return response.data;
  },

  getBuildingUsers: async (buildingId: string) => {
    const response = await api.get(
      `/user-management/buildings/${buildingId}/users`
    );
    return response.data;
  },

  removeUserFromBuilding: async (buildingId: string, userId: string) => {
    const response = await api.delete(
      `/user-management/buildings/${buildingId}/users/${userId}`
    );
    return response.data;
  },

  addUserToBuilding: async (buildingId: string, username: string) => {
    const response = await api.post(
      `/user-management/buildings/${buildingId}/add-user`,
      { username }
    );
    return response.data;
  },

  getMyBuildings: async () => {
    const response = await api.get("/user-management/my-buildings");
    return response.data;
  },
};
