import { billingAPI } from "@/lib/api";
import {
  BillingCycle,
  BillingEntry,
  BillingHistoryEntry,
  BuildingPricing,
  PaymentEntry,
} from "@/types";
import { getCurrentDateUTC7 } from "@/lib/utils";

export const billingService = {
  // Get billing entries for a room
  async getBillingEntriesByRoom(
    roomId: string
  ): Promise<{ data: BillingEntry[] | null; error: string | null }> {
    try {
      const data = await billingAPI.getEntries(roomId);
      return { data, error: null };
    } catch (error: any) {
      console.error("Error fetching billing entries:", error);
      return {
        data: null,
        error:
          error.response?.data?.message || "Failed to fetch billing entries",
      };
    }
  },

  // Get building pricing
  async getBuildingPricing(buildingId: string): Promise<{
    data: BuildingPricing | null;
    error: string | null;
  }> {
    try {
      const data = await billingAPI.getBuildingPricing(buildingId);
      return { data, error: null };
    } catch (error: any) {
      console.error("Error fetching building pricing:", error);
      return {
        data: null,
        error:
          error.response?.data?.message || "Failed to fetch building pricing",
      };
    }
  },

  // Update building pricing
  async updateBuildingPricing(
    buildingId: string,
    waterPrice: number,
    electricityPrice: number,
    billingEnabled: boolean,
    isBillingEntryToggled: boolean,
    billingEnabledDate: string
  ): Promise<{ data: BuildingPricing | null; error: string | null }> {
    try {
      const data = await billingAPI.updateBuildingPricing(buildingId, {
        waterPricePerUnit: waterPrice,
        electricityPricePerUnit: electricityPrice,
        billingEnabled,
        isBillingEntryToggled,
        billingEnabledDate,
      });
      return { data, error: null };
    } catch (error: any) {
      console.error("Error updating building pricing:", error);
      return {
        data: null,
        error:
          error.response?.data?.message || "Failed to update building pricing",
      };
    }
  },

  // Get billing toggle validation status
  async getBillingToggleValidation(buildingId: string): Promise<{
    data: {
      canToggleOff: boolean;
      billingEnabled: boolean;
      billingEnabledDate: string | null;
      roomsWithoutBilling: string[];
      roomsWithOldBilling: string[];
      message: string;
    } | null;
    error: string | null;
  }> {
    try {
      const data = await billingAPI.getBillingToggleValidation(buildingId);
      return { data, error: null };
    } catch (error: any) {
      console.error("Error getting billing toggle validation:", error);
      return {
        data: null,
        error:
          error.response?.data?.message || "Failed to get validation status",
      };
    }
  },

  // Create billing entry
  async createBillingEntry(
    roomId: string,
    initialWaterReading: number,
    initialElectricityReading: number,
    waterReading: number,
    electricityReading: number,
    currentBillingCycleId: string
  ): Promise<{ data: BillingEntry | null; error: string | null }> {
    try {
      const data = await billingAPI.createEntry(roomId, {
        billingDate: getCurrentDateUTC7(),
        initialWaterReading,
        initialElectricityReading,
        waterReading,
        electricityReading,
        currentBillingCycleId,
      });
      return { data, error: null };
    } catch (error: any) {
      console.error("Error creating billing entry:", error);
      return {
        data: null,
        error:
          error.response?.data?.message || "Failed to create billing entry",
      };
    }
  },

  //Get Billing Cycles
  async getBillingCycles(
    buildingId: string
  ): Promise<{ data: BillingCycle[] | null; error: string | null }> {
    try {
      const data = await billingAPI.getBillingCycles(buildingId);
      return { data, error: null };
    } catch (error: any) {
      console.error("Error fetching billing cycles:", error);
      return {
        data: null,
        error:
          error.response?.data?.message || "Failed to fetch billing cycles",
      };
    }
  },

  //Get Billing History By Cycle Id
  async getBillingHistoryByCycleId(
    buildingId: string,
    cycleId: string
  ): Promise<{ data: BillingHistoryEntry[] | null; error: string | null }> {
    try {
      const data = await billingAPI.getBillingHistoryByCycleId(
        buildingId,
        cycleId
      );
      return { data, error: null };
    } catch (error: any) {
      console.error("Error fetching billing history:", error);
      return {
        data: null,
        error:
          error.response?.data?.message || "Failed to fetch billing history",
      };
    }
  },

  // Get billing summary for a room
  async getRoomBillingSummary(roomId: string): Promise<{
    data: {
      totalBilled: number;
      lastBillingDate: string | null;
      averageMonthlyBill: number;
      entryCount: number;
    } | null;
    error: string | null;
  }> {
    try {
      const { data: entries, error } = await this.getBillingEntriesByRoom(
        roomId
      );

      if (error) {
        return { data: null, error };
      }

      if (!entries || entries.length === 0) {
        return {
          data: {
            totalBilled: 0,
            lastBillingDate: null,
            averageMonthlyBill: 0,
            entryCount: 0,
          },
          error: null,
        };
      }

      const totalBilled = entries.reduce(
        (sum, entry) => sum + entry.totalAmount,
        0
      );
      const lastBillingDate = entries[0].billingDate; // Already sorted by date desc
      const averageMonthlyBill = totalBilled / entries.length;

      return {
        data: {
          totalBilled,
          lastBillingDate,
          averageMonthlyBill,
          entryCount: entries.length,
        },
        error: null,
      };
    } catch (error) {
      return { data: null, error: "Failed to get billing summary" };
    }
  },

  // Add payment to billing entry
  async addPayment(
    billingId: string,
    amount: number,
    paymentDate: string,
    paymentMethodName: string = "Cash"
  ): Promise<{ data: PaymentEntry | null; error: string | null }> {
    try {
      const data = await billingAPI.addPayment(billingId, {
        amount,
        paymentDate,
        paymentMethodName,
      });
      return { data, error: null };
    } catch (error: any) {
      console.error("Error adding payment:", error);
      return {
        data: null,
        error: error.response?.data?.message || "Failed to add payment",
      };
    }
  },

  // Get payment history for billing entry
  async getPaymentHistory(
    billingId: string
  ): Promise<{ data: PaymentEntry[] | null; error: string | null }> {
    try {
      const data = await billingAPI.getPaymentHistory(billingId);
      return { data, error: null };
    } catch (error: any) {
      console.error("Error fetching payment history:", error);
      return {
        data: null,
        error:
          error.response?.data?.message || "Failed to fetch payment history",
      };
    }
  },

  // Add payment entry
  async addPaymentEntry(
    billingId: string,
    amount: number,
    paymentDate: string,
    paymentMethodName: string
  ): Promise<{ data: PaymentEntry | null; error: string | null }> {
    try {
      const data = await billingAPI.addPayment(billingId, {
        amount,
        paymentDate,
        paymentMethodName,
      });
      return { data, error: null };
    } catch (error: any) {
      console.error("Error adding payment entry:", error);
      return {
        data: null,
        error: error.response?.data?.message || "Failed to add payment entry",
      };
    }
  },

  async getCurrentBillingCycle(buildingId: string): Promise<{
    data: BillingCycle | null;
    error: string | null;
  }> {
    try {
      const data = await billingAPI.getCurrentBillingCycle(buildingId);
      return { data, error: null };
    } catch (error: any) {
      console.error("Error fetching billing cycle status:", error);
      return {
        data: null,
        error:
          error.response?.data?.message ||
          "Failed to fetch billing cycle status",
      };
    }
  },
};
