import { buildingsAPI } from "@/lib/api";
import { Building } from "@/types";

export const buildingService = {
  // Get all buildings
  async getBuildings(): Promise<{
    data: Building[] | null;
    error: string | null;
  }> {
    try {
      const data = await buildingsAPI.getAll();
      return { data, error: null };
    } catch (error: any) {
      console.error("Error fetching buildings:", error);
      return {
        data: null,
        error: error.response?.data?.message || "Failed to fetch buildings",
      };
    }
  },

  // Get building by ID
  async getBuildingById(
    id: string
  ): Promise<{ data: Building | null; error: string | null }> {
    try {
      const data = await buildingsAPI.getById(id);
      return { data, error: null };
    } catch (error: any) {
      console.error("Error fetching building:", error);
      return {
        data: null,
        error: error.response?.data?.message || "Failed to fetch building",
      };
    }
  },

  // Create new building
  async createBuilding(
    name: string,
    userId: string
  ): Promise<{ data: Building | null; error: string | null }> {
    try {
      const data = await buildingsAPI.create({ name: name.trim() });
      return { data, error: null };
    } catch (error: any) {
      console.error("Error creating building:", error);
      return {
        data: null,
        error: error.response?.data?.message || "Failed to create building",
      };
    }
  },

  // Update building
  async updateBuilding(
    id: string,
    name: string
  ): Promise<{ data: Building | null; error: string | null }> {
    try {
      const data = await buildingsAPI.update(id, { name: name.trim() });
      return { data, error: null };
    } catch (error: any) {
      console.error("Error updating building:", error);
      return {
        data: null,
        error: error.response?.data?.message || "Failed to update building",
      };
    }
  },

  // Delete building
  async deleteBuilding(id: string): Promise<{ error: string | null }> {
    try {
      await buildingsAPI.delete(id);
      return { error: null };
    } catch (error: any) {
      console.error("Error deleting building:", error);
      return {
        error: error.response?.data?.message || "Failed to delete building",
      };
    }
  },

  // Get building stats
  async getBuildingStats(buildingId: string): Promise<{
    data: {
      totalRooms: number;
      occupiedRooms: number;
      monthlyRevenue: number;
    } | null;
    error: string | null;
  }> {
    try {
      // Get building with rooms data
      const building = await buildingsAPI.getById(buildingId);

      if (!building || !building.rooms) {
        return {
          data: {
            totalRooms: 0,
            occupiedRooms: 0,
            monthlyRevenue: 0,
          },
          error: null,
        };
      }

      const totalRooms = building.rooms.length;
      const occupiedRooms = building.rooms.filter(
        (room: any) => room.status === "OCCUPIED"
      ).length;
      const monthlyRevenue = building.rooms
        .filter((room: any) => room.status === "OCCUPIED")
        .reduce(
          (sum: number, room: any) => sum + parseFloat(room.monthlyRent),
          0
        );

      return {
        data: {
          totalRooms,
          occupiedRooms,
          monthlyRevenue,
        },
        error: null,
      };
    } catch (error: any) {
      console.error("Error fetching building stats:", error);
      return {
        data: null,
        error:
          error.response?.data?.message || "Failed to fetch building stats",
      };
    }
  },
};
